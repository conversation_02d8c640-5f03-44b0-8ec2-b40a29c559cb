#!/usr/bin/env python3
"""
Debug problemów z obliczaniem PnL i panelem monitorowania na żywo.
"""

import sqlite3
import pandas as pd
from datetime import datetime

def debug_pnl_issues():
    """Debuguje problemy z PnL i statusami sygnałów."""
    
    conn = sqlite3.connect('signals.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("🔍 DEBUG PROBLEMÓW Z PnL I PANELEM MONITOROWANIA")
    print("=" * 60)
    
    # 1. Sprawdź najnowsze sygnały
    print("\n📊 NAJNOWSZE SYGNAŁY (ostatnie 15):")
    cursor.execute("""
        SELECT id, pair, status, entry, tp, sl, exit_price, pnl, entry_hit_price, 
               close_timestamp, entry_hit_timestamp
        FROM signals 
        WHERE id >= 15 
        ORDER BY id DESC 
        LIMIT 15
    """)
    
    signals = cursor.fetchall()
    print(f"{'ID':>3} | {'Pair':>8} | {'Status':>9} | {'Entry':>8} | {'TP':>8} | {'SL':>8} | {'Exit':>8} | {'PnL':>8} | {'Entry_Hit':>8}")
    print("-" * 90)
    
    for signal in signals:
        pnl_str = f"{signal['pnl']:.4f}" if signal['pnl'] is not None else "None"
        exit_str = f"{signal['exit_price']:.4f}" if signal['exit_price'] is not None else "None"
        entry_hit_str = f"{signal['entry_hit_price']:.4f}" if signal['entry_hit_price'] is not None else "None"
        
        print(f"{signal['id']:>3} | {signal['pair']:>8} | {signal['status']:>9} | "
              f"{signal['entry']:>8.4f} | {signal['tp']:>8.4f} | {signal['sl']:>8.4f} | "
              f"{exit_str:>8} | {pnl_str:>8} | {entry_hit_str:>8}")
    
    # 2. Sprawdź sygnały z problemami PnL
    print(f"\n⚠️  SYGNAŁY Z PROBLEMAMI PnL:")
    cursor.execute("""
        SELECT id, pair, status, entry, tp, sl, exit_price, pnl
        FROM signals 
        WHERE status IN ('TP_HIT', 'SL_HIT') AND (pnl IS NULL OR pnl = 0)
        ORDER BY id DESC
    """)
    
    problematic_signals = cursor.fetchall()
    if problematic_signals:
        print(f"Znaleziono {len(problematic_signals)} sygnałów z problemami:")
        for signal in problematic_signals:
            print(f"  ID {signal['id']}: {signal['pair']} - {signal['status']} - PnL: {signal['pnl']}")
    else:
        print("Brak sygnałów z problemami PnL")
    
    # 3. Sprawdź sygnały bez exit_price ale z statusem zamkniętym
    print(f"\n⚠️  SYGNAŁY BEZ EXIT_PRICE:")
    cursor.execute("""
        SELECT id, pair, status, entry, tp, sl, exit_price, pnl
        FROM signals 
        WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND exit_price IS NULL
        ORDER BY id DESC
    """)
    
    no_exit_signals = cursor.fetchall()
    if no_exit_signals:
        print(f"Znaleziono {len(no_exit_signals)} sygnałów bez exit_price:")
        for signal in no_exit_signals:
            print(f"  ID {signal['id']}: {signal['pair']} - {signal['status']}")
    else:
        print("Wszystkie zamknięte sygnały mają exit_price")
    
    # 4. Sprawdź sygnały ENTRY_HIT bez entry_hit_price
    print(f"\n⚠️  SYGNAŁY ENTRY_HIT BEZ ENTRY_HIT_PRICE:")
    cursor.execute("""
        SELECT id, pair, status, entry, entry_hit_price, entry_hit_timestamp
        FROM signals 
        WHERE status = 'ENTRY_HIT' AND entry_hit_price IS NULL
        ORDER BY id DESC
    """)
    
    no_entry_hit_signals = cursor.fetchall()
    if no_entry_hit_signals:
        print(f"Znaleziono {len(no_entry_hit_signals)} sygnałów ENTRY_HIT bez entry_hit_price:")
        for signal in no_entry_hit_signals:
            print(f"  ID {signal['id']}: {signal['pair']}")
    else:
        print("Wszystkie sygnały ENTRY_HIT mają entry_hit_price")
    
    # 5. Sprawdź sygnały NEW które powinny być ENTRY_HIT
    print(f"\n🔍 ANALIZA SYGNAŁÓW NEW:")
    cursor.execute("""
        SELECT id, pair, side, entry, timestamp
        FROM signals 
        WHERE status = 'NEW'
        ORDER BY id DESC
    """)
    
    new_signals = cursor.fetchall()
    print(f"Znaleziono {len(new_signals)} sygnałów ze statusem NEW")
    
    # 6. Test obliczania PnL
    print(f"\n🧮 TEST OBLICZANIA PnL:")
    
    # Przykład sygnału TP_HIT
    cursor.execute("""
        SELECT id, pair, side, entry, tp, sl, exit_price, pnl
        FROM signals 
        WHERE status = 'TP_HIT' AND pnl IS NOT NULL
        LIMIT 1
    """)
    
    tp_signal = cursor.fetchone()
    if tp_signal:
        # Oblicz PnL manualnie
        entry = tp_signal['entry']
        exit_price = tp_signal['exit_price']
        side = tp_signal['side']
        stored_pnl = tp_signal['pnl']
        
        if side == 'BUY':
            calculated_pnl = (exit_price - entry) / entry
        else:  # SELL
            calculated_pnl = (entry - exit_price) / entry
        
        print(f"Sygnał ID {tp_signal['id']} ({tp_signal['pair']}):")
        print(f"  Side: {side}")
        print(f"  Entry: {entry}")
        print(f"  Exit: {exit_price}")
        print(f"  Stored PnL: {stored_pnl:.6f}")
        print(f"  Calculated PnL: {calculated_pnl:.6f}")
        print(f"  Różnica: {abs(stored_pnl - calculated_pnl):.6f}")
    
    # 7. Sprawdź aktywność monitora
    print(f"\n📡 SPRAWDZENIE AKTYWNOŚCI MONITORA:")
    cursor.execute("""
        SELECT COUNT(*) as count
        FROM signals 
        WHERE status IN ('NEW', 'ENTRY_HIT')
    """)
    
    active_signals = cursor.fetchone()['count']
    print(f"Sygnały do monitorowania: {active_signals}")
    
    # 8. Sprawdź ostatnie aktualizacje
    print(f"\n⏰ OSTATNIE AKTUALIZACJE:")
    cursor.execute("""
        SELECT id, pair, status, close_timestamp, entry_hit_timestamp
        FROM signals 
        WHERE close_timestamp IS NOT NULL OR entry_hit_timestamp IS NOT NULL
        ORDER BY COALESCE(close_timestamp, entry_hit_timestamp) DESC
        LIMIT 5
    """)
    
    recent_updates = cursor.fetchall()
    for signal in recent_updates:
        last_update = signal['close_timestamp'] or signal['entry_hit_timestamp']
        print(f"  ID {signal['id']}: {signal['pair']} - {signal['status']} - {last_update}")
    
    conn.close()
    
    print(f"\n🎯 REKOMENDACJE:")
    print("1. Sprawdź czy bot monitorujący działa (discord_bybit_signal_monitor.py)")
    print("2. Sprawdź logi bota pod kątem błędów API Bybit")
    print("3. Sprawdź czy sygnały NEW są poprawnie aktualizowane do ENTRY_HIT")
    print("4. Sprawdź czy PnL jest obliczany przy zamykaniu sygnałów")
    print("5. Sprawdź czy dashboard odświeża się automatycznie")

if __name__ == "__main__":
    debug_pnl_issues()
