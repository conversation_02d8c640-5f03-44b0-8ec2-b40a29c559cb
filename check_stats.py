#!/usr/bin/env python3
"""
Skrypt do analizy problemów ze statystykami w bazie danych sygnałów.
"""

import sqlite3
import pandas as pd
import numpy as np
import math
from datetime import datetime, timedelta

def analyze_database():
    """Analizuje bazę danych i identyfikuje problemy ze statystykami."""
    
    conn = sqlite3.connect('signals.db')
    conn.row_factory = sqlite3.Row
    
    print("=== ANALIZA BAZY DANYCH SYGNAŁÓW ===\n")
    
    # 1. Podstawowe statystyki
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) as total FROM signals")
    total_signals = cursor.fetchone()['total']
    print(f"📊 Całkowita liczba sygnałów: {total_signals}")
    
    # 2. Rozkład statusów
    cursor.execute("SELECT status, COUNT(*) as count FROM signals GROUP BY status ORDER BY count DESC")
    status_dist = cursor.fetchall()
    print("\n📈 Rozkład statusów:")
    for row in status_dist:
        print(f"  {row['status']}: {row['count']}")
    
    # 3. Sygnały z PnL
    cursor.execute("SELECT COUNT(*) as count FROM signals WHERE pnl IS NOT NULL")
    signals_with_pnl = cursor.fetchone()['count']
    print(f"\n💰 Sygnały z PnL: {signals_with_pnl}")
    
    # 4. Problematyczne wartości PnL
    cursor.execute("SELECT COUNT(*) as count FROM signals WHERE pnl IS NOT NULL AND (pnl = 'NaN' OR pnl = 'Infinity' OR pnl = '-Infinity')")
    problematic_pnl = cursor.fetchone()['count']
    print(f"⚠️  Problematyczne wartości PnL: {problematic_pnl}")
    
    # 5. Zakres wartości PnL
    cursor.execute("SELECT MIN(pnl) as min_pnl, MAX(pnl) as max_pnl, AVG(pnl) as avg_pnl FROM signals WHERE pnl IS NOT NULL")
    pnl_stats = cursor.fetchone()
    print(f"\n📊 Statystyki PnL:")
    print(f"  Min: {pnl_stats['min_pnl']:.4f}")
    print(f"  Max: {pnl_stats['max_pnl']:.4f}")
    print(f"  Avg: {pnl_stats['avg_pnl']:.4f}")
    
    # 6. Sprawdź sygnały bez close_timestamp ale z PnL
    cursor.execute("SELECT COUNT(*) as count FROM signals WHERE pnl IS NOT NULL AND close_timestamp IS NULL")
    no_close_time = cursor.fetchone()['count']
    print(f"\n⚠️  Sygnały z PnL ale bez close_timestamp: {no_close_time}")
    
    # 7. Sprawdź filtry czasowe
    print("\n🕐 Analiza filtrów czasowych:")
    for days in [1, 7, 30]:
        cursor.execute(f"""
            SELECT COUNT(*) as count 
            FROM signals 
            WHERE timestamp >= datetime('now', '-{days} days')
        """)
        count = cursor.fetchone()['count']
        print(f"  Ostatnie {days} dni: {count} sygnałów")
    
    # 8. Sprawdź problemy z timeframe_min
    cursor.execute("SELECT DISTINCT timeframe_min FROM signals WHERE timeframe_min IS NOT NULL ORDER BY timeframe_min")
    timeframes = cursor.fetchall()
    print(f"\n⏱️  Dostępne timeframe_min: {[row['timeframe_min'] for row in timeframes]}")
    
    # 9. Sprawdź pary walutowe
    cursor.execute("SELECT pair, COUNT(*) as count FROM signals GROUP BY pair ORDER BY count DESC LIMIT 10")
    pairs = cursor.fetchall()
    print(f"\n💱 Top 10 par walutowych:")
    for row in pairs:
        print(f"  {row['pair']}: {row['count']}")
    
    # 10. Sprawdź problemy z obliczeniami
    print("\n🔍 TESTOWANIE OBLICZEŃ STATYSTYK:")
    
    # Test Win Rate
    cursor.execute("SELECT COUNT(*) as total FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')")
    closed_signals = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as wins FROM signals WHERE pnl > 0")
    winning_signals = cursor.fetchone()['wins']
    
    if closed_signals > 0:
        win_rate = (winning_signals / closed_signals) * 100
        print(f"  Win Rate: {win_rate:.2f}% ({winning_signals}/{closed_signals})")
    else:
        print("  Win Rate: Brak zamkniętych sygnałów")
    
    # Test Sharpe Ratio
    cursor.execute("SELECT pnl FROM signals WHERE pnl IS NOT NULL ORDER BY close_timestamp")
    pnl_data = [row['pnl'] for row in cursor.fetchall()]
    
    if pnl_data:
        pnl_series = pd.Series(pnl_data)
        pnl_mean = pnl_series.mean()
        pnl_std = pnl_series.std()
        
        print(f"  PnL Mean: {pnl_mean:.6f}")
        print(f"  PnL Std: {pnl_std:.6f}")
        
        if pnl_std > 0 and not pd.isna(pnl_std):
            sharpe_ratio = pnl_mean / pnl_std
            print(f"  Sharpe Ratio: {sharpe_ratio:.4f}")
        else:
            print("  Sharpe Ratio: Nie można obliczyć (std = 0 lub NaN)")
    
    # Test Max Drawdown
    if pnl_data:
        cumulative_pnl = pnl_series.cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - running_max
        max_drawdown = abs(drawdown.min()) if not drawdown.empty else 0.0
        print(f"  Max Drawdown: {max_drawdown:.6f}")
    
    # Test Profit Factor
    if pnl_data:
        total_wins = pnl_series[pnl_series > 0].sum()
        total_losses = abs(pnl_series[pnl_series < 0].sum())
        
        print(f"  Total Wins: {total_wins:.6f}")
        print(f"  Total Losses: {total_losses:.6f}")
        
        if total_losses > 0:
            profit_factor = total_wins / total_losses
            print(f"  Profit Factor: {profit_factor:.4f}")
        else:
            print(f"  Profit Factor: {'∞' if total_wins > 0 else '0'} (brak strat)")
    
    conn.close()
    
    print("\n=== KONIEC ANALIZY ===")

if __name__ == "__main__":
    analyze_database()
