#!/usr/bin/env python3
"""
Test sygnałów z rzeczywistym PnL.
"""

import requests

def test_real_pnl():
    """Test sygnałów z rzeczywistym PnL."""
    
    print("🔍 TEST SYGNAŁÓW Z RZECZYWISTYM PnL")
    print("=" * 40)
    
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=30')
        if response.status_code == 200:
            signals = response.json()
            
            print(f"Pobrano {len(signals)} sygnałów")
            print()
            
            # Znajdź sygnały z PnL
            signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
            print(f"Sygnały z PnL: {len(signals_with_pnl)}")
            
            if signals_with_pnl:
                print("\nSygnały z rzeczywistym PnL:")
                for signal in signals_with_pnl:
                    pnl = signal.get('pnl')
                    pnl_percent = signal.get('pnl_percent')
                    print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                    print(f"  PnL: {pnl} ({type(pnl).__name__})")
                    print(f"  PnL %: {pnl_percent}")
                    print()
            else:
                print("Brak sygnałów z PnL")
                
            # Sprawdź statusy
            print("Rozkład statusów:")
            status_counts = {}
            for signal in signals:
                status = signal['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                print(f"  {status}: {count}")
                
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")

if __name__ == "__main__":
    test_real_pnl()
