#!/usr/bin/env python3
"""
Production-Ready Discord Bybit Signal Monitor Dashboard

Kompletna, gotowa do produkcji aplikacja Flask do analizy sygnałów tradingowych.
Zawiera zaawansowane metryki, real-time monitoring, i profesjonalny UI.

Autor: AI Assistant
Data: 2025-06-15
Wersja: 2.0 Production
"""

import os
import sqlite3
import json
import math
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from flask import Flask, render_template, jsonify, request, abort
from flask_socketio import SocketIO, emit
import pandas as pd
import numpy as np
from dotenv import load_dotenv

# === CONFIGURATION ===
@dataclass
class Config:
    """Klasa konfiguracyjna aplikacji."""
    DB_PATH: str
    SECRET_KEY: str
    DEBUG: bool = False
    HOST: str = '0.0.0.0'
    PORT: int = 5000

    @classmethod
    def from_env(cls) -> 'Config':
        """Tworzy konfigurację z zmiennych środowiskowych."""
        load_dotenv()
        return cls(
            DB_PATH=os.getenv('DB_PATH', 'signals.db'),
            SECRET_KEY=os.getenv('SECRET_KEY', 'production-secret-key-change-me'),
            DEBUG=os.getenv('DEBUG', 'False').lower() == 'true',
            HOST=os.getenv('HOST', '0.0.0.0'),
            PORT=int(os.getenv('PORT', '5000'))
        )

# === LOGGING SETUP ===
def setup_logging() -> logging.Logger:
    """Konfiguruje system logowania."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dashboard.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# === UTILITIES ===
class JSONSanitizer:
    """Klasa do sanityzacji danych JSON."""

    @staticmethod
    def sanitize(obj: Any) -> Any:
        """
        Sanityzuje obiekt do bezpiecznego formatu JSON.

        Args:
            obj: Obiekt do sanityzacji

        Returns:
            Obiekt z zastąpionymi problematycznymi wartościami
        """
        if isinstance(obj, dict):
            return {key: JSONSanitizer.sanitize(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [JSONSanitizer.sanitize(item) for item in obj]
        elif isinstance(obj, float):
            if math.isnan(obj):
                return 0.0
            elif math.isinf(obj):
                return 999999.0 if obj > 0 else -999999.0
            return obj
        elif isinstance(obj, (np.floating, np.integer)):
            if np.isnan(obj):
                return 0.0
            elif np.isinf(obj):
                return 999999.0 if obj > 0 else -999999.0
            return float(obj) if isinstance(obj, np.floating) else int(obj)
        return obj

class DatabaseManager:
    """Klasa do zarządzania połączeniami z bazą danych."""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

    def get_connection(self) -> sqlite3.Connection:
        """Uzyskaj połączenie z bazą danych z error handling."""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Umożliwia dostęp do kolumn po nazwie
            return conn
        except sqlite3.Error as e:
            self.logger.error(f"Database connection error: {e}")
            raise

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Wykonaj zapytanie SQL z error handling."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()
                return [dict(zip(columns, row)) for row in rows]
        except sqlite3.Error as e:
            self.logger.error(f"Query execution error: {e}")
            raise

class SignalAnalyzer:
    """Klasa do analizy sygnałów tradingowych."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.logger = logging.getLogger(__name__)

    def get_all_signals(self, limit: Optional[int] = None) -> pd.DataFrame:
        """Pobierz wszystkie sygnały z bazy."""
        try:
            query = """
            SELECT id, message_id, pair, side, entry, tp, sl,
                   timestamp, timeframe_min, status, close_timestamp,
                   exit_price, pnl, entry_hit_timestamp, entry_hit_price
            FROM signals
            ORDER BY timestamp DESC
            """
            if limit:
                query += f" LIMIT {limit}"

            with self.db.get_connection() as conn:
                # POPRAWKA: Użyj raw SQL zamiast pandas żeby zachować NULL wartości
                cursor = conn.cursor()
                cursor.execute(query)
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()

                # Konwertuj do DataFrame zachowując NULL jako None
                data = []
                for row in rows:
                    row_dict = {}
                    for i, col in enumerate(columns):
                        row_dict[col] = row[i]
                    data.append(row_dict)

                # POPRAWKA: Użyj dtype=object dla kolumn z None
                df = pd.DataFrame(data)
                # Upewnij się, że kolumny z None pozostają jako object
                for col in ['pnl', 'exit_price', 'entry_hit_price']:
                    if col in df.columns:
                        df[col] = df[col].astype('object')

            return df
        except Exception as e:
            self.logger.error(f"Error fetching signals: {e}")
            return pd.DataFrame()

    def get_statistics(self, days_filter: Optional[int] = None) -> Dict[str, Any]:
        """
        Oblicz rozszerzone statystyki sygnałów z poprawionymi kalkulacjami.

        Kluczowe poprawki:
        - Win Rate obliczany tylko na zamkniętych sygnałach (TP_HIT, SL_HIT, EXPIRED)
        - Winning signals to sygnały z PnL > 0 ORAZ status zamknięty
        - Ujednolicone filtry statusów
        - Dodana walidacja danych
        """
        try:
            time_filter = ""
            if days_filter:
                time_filter = f"AND timestamp >= datetime('now', '-{days_filter} days')"

            # Podstawowe statystyki - POPRAWIONE
            stats_query = f"""
            SELECT
                COUNT(*) as total_signals,
                COUNT(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') THEN 1 END) as closed_signals,
                COUNT(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl > 0 THEN 1 END) as winning_signals,
                COUNT(CASE WHEN status = 'TP_HIT' THEN 1 END) as tp_hits,
                COUNT(CASE WHEN status = 'SL_HIT' THEN 1 END) as sl_hits,
                COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_signals,
                COUNT(CASE WHEN status = 'NEW' THEN 1 END) as new_signals,
                COUNT(CASE WHEN status = 'ENTRY_HIT' THEN 1 END) as entry_hit_signals,
                AVG(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl IS NOT NULL THEN pnl END) as avg_pnl,
                MAX(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl IS NOT NULL THEN pnl END) as max_pnl,
                MIN(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl IS NOT NULL THEN pnl END) as min_pnl,
                SUM(CASE WHEN status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl IS NOT NULL THEN pnl END) as total_pnl
            FROM signals
            WHERE 1=1 {time_filter}
            """

            results = self.db.execute_query(stats_query)
            if not results:
                return self._get_empty_stats()

            stats = results[0]

            # Walidacja danych
            closed_signals = stats['closed_signals'] or 0
            winning_signals = stats['winning_signals'] or 0

            # Oblicz Win Rate tylko dla zamkniętych sygnałów
            win_rate = (winning_signals / closed_signals * 100) if closed_signals > 0 else 0

            # Zaawansowane metryki
            advanced_stats = self._calculate_advanced_metrics(time_filter)

            # Statystyki per para
            pair_stats = self._get_pair_statistics(time_filter)

            # Statystyki per timeframe
            tf_stats = self._get_timeframe_statistics(time_filter)

            return {
                'total_signals': stats['total_signals'] or 0,
                'closed_signals': closed_signals,
                'winning_signals': winning_signals,
                'tp_hits': stats['tp_hits'] or 0,
                'sl_hits': stats['sl_hits'] or 0,
                'timeouts': stats['expired_signals'] or 0,
                'new_signals': stats['new_signals'] or 0,
                'entry_hit_signals': stats['entry_hit_signals'] or 0,
                'avg_pnl': stats['avg_pnl'] or 0,
                'max_pnl': stats['max_pnl'] or 0,
                'min_pnl': stats['min_pnl'] or 0,
                'total_pnl': stats['total_pnl'] or 0,
                'win_rate': win_rate,
                'pair_stats': pair_stats,
                'timeframe_stats': tf_stats,
                **advanced_stats
            }
        except Exception as e:
            self.logger.error(f"Error calculating statistics: {e}")
            return self._get_empty_stats()

    def _get_empty_stats(self) -> Dict[str, Any]:
        """Zwraca puste statystyki w przypadku błędu."""
        return {
            'total_signals': 0,
            'closed_signals': 0,
            'winning_signals': 0,
            'tp_hits': 0,
            'sl_hits': 0,
            'timeouts': 0,
            'new_signals': 0,
            'entry_hit_signals': 0,
            'avg_pnl': 0,
            'max_pnl': 0,
            'min_pnl': 0,
            'total_pnl': 0,
            'win_rate': 0,
            'pair_stats': [],
            'timeframe_stats': [],
            'sharpe_ratio': 0,
            'sortino_ratio': 0,  # NOWA METRYKA
            'max_drawdown': 0,
            'calmar_ratio': 0,  # NOWA METRYKA
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0,
            'profit_factor': 0,
            'avg_win': 0,
            'avg_loss': 0
        }

    def _calculate_advanced_metrics(self, time_filter: str = "") -> Dict[str, float]:
        """Oblicz zaawansowane metryki wydajności."""
        try:
            # POPRAWIONE: tylko zamknięte sygnały z PnL
            pnl_query = f"""
            SELECT pnl, close_timestamp
            FROM signals
            WHERE pnl IS NOT NULL
              AND status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
              AND close_timestamp IS NOT NULL
              {time_filter}
            ORDER BY close_timestamp
            """

            results = self.db.execute_query(pnl_query)
            if not results or len(results) < 2:  # Potrzeba minimum 2 punkty danych
                return self._get_empty_advanced_metrics()

            pnl_series = pd.Series([r['pnl'] for r in results])

            # Walidacja danych
            if pnl_series.empty or pnl_series.isna().all():
                return self._get_empty_advanced_metrics()

            # 1. SHARPE RATIO - poprawiony z annualizacją
            pnl_mean = pnl_series.mean()
            pnl_std = pnl_series.std()

            if pnl_std > 0 and not pd.isna(pnl_std) and not pd.isna(pnl_mean):
                sharpe_ratio = pnl_mean / pnl_std
                # Annualizacja (zakładając dzienne dane)
                sharpe_ratio = sharpe_ratio * np.sqrt(252) if not pd.isna(sharpe_ratio) else 0.0
            else:
                sharpe_ratio = 0.0

            # 2. SORTINO RATIO - nowa metryka (tylko downside risk)
            downside_returns = pnl_series[pnl_series < 0]
            if len(downside_returns) > 0:
                downside_std = downside_returns.std()
                sortino_ratio = (pnl_mean / downside_std * np.sqrt(252)) if downside_std > 0 else 0.0
            else:
                sortino_ratio = 999999.0 if pnl_mean > 0 else 0.0

            # 3. MAX DRAWDOWN - poprawiony z relatywnym obliczeniem
            cumulative_pnl = pnl_series.cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max) / running_max.where(running_max != 0, 1)
            max_drawdown = abs(drawdown.min()) if not drawdown.empty and not pd.isna(drawdown.min()) else 0.0

            # 4. CALMAR RATIO - nowa metryka (annual return / max drawdown)
            annual_return = pnl_mean * 252  # Annualizacja
            calmar_ratio = (annual_return / max_drawdown) if max_drawdown > 0 else 0.0

            # 5. Consecutive wins/losses
            wins_losses = (pnl_series > 0).astype(int)
            consecutive_wins = self._max_consecutive(wins_losses, 1)
            consecutive_losses = self._max_consecutive(wins_losses, 0)

            # 6. PROFIT FACTOR - poprawiony z walidacją
            total_wins = pnl_series[pnl_series > 0].sum()
            total_losses = abs(pnl_series[pnl_series < 0].sum())

            if pd.isna(total_wins):
                total_wins = 0.0
            if pd.isna(total_losses):
                total_losses = 0.0

            if total_losses > 0:
                profit_factor = total_wins / total_losses
            else:
                profit_factor = 999999.0 if total_wins > 0 else 0.0

            # 7. Średnie zyski/straty
            avg_win = pnl_series[pnl_series > 0].mean() if (pnl_series > 0).any() else 0.0
            avg_loss = pnl_series[pnl_series < 0].mean() if (pnl_series < 0).any() else 0.0

            # Sanityzacja wyników
            return {
                'sharpe_ratio': float(sharpe_ratio) if not pd.isna(sharpe_ratio) else 0.0,
                'sortino_ratio': float(sortino_ratio) if not pd.isna(sortino_ratio) else 0.0,
                'max_drawdown': float(max_drawdown) if not pd.isna(max_drawdown) else 0.0,
                'calmar_ratio': float(calmar_ratio) if not pd.isna(calmar_ratio) else 0.0,
                'max_consecutive_wins': int(consecutive_wins),
                'max_consecutive_losses': int(consecutive_losses),
                'profit_factor': float(profit_factor) if not pd.isna(profit_factor) else 0.0,
                'avg_win': float(avg_win) if not pd.isna(avg_win) else 0.0,
                'avg_loss': float(avg_loss) if not pd.isna(avg_loss) else 0.0
            }
        except Exception as e:
            self.logger.error(f"Error calculating advanced metrics: {e}")
            return self._get_empty_advanced_metrics()

    def _get_empty_advanced_metrics(self) -> Dict[str, float]:
        """Zwraca puste zaawansowane metryki."""
        return {
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'max_drawdown': 0.0,
            'calmar_ratio': 0.0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0,
            'profit_factor': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0
        }

    def _max_consecutive(self, series: pd.Series, value: int) -> int:
        """Oblicz maksymalną liczbę kolejnych wystąpień wartości."""
        if series.empty:
            return 0

        consecutive = 0
        max_consecutive = 0

        for val in series:
            if val == value:
                consecutive += 1
                max_consecutive = max(max_consecutive, consecutive)
            else:
                consecutive = 0

        return max_consecutive

    def _get_pair_statistics(self, time_filter: str = "") -> List[Dict[str, Any]]:
        """Pobierz statystyki per para - POPRAWIONE."""
        try:
            pair_stats_query = f"""
            SELECT pair,
                   COUNT(*) as count,
                   COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
                   AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
                   SUM(CASE WHEN pnl IS NOT NULL THEN pnl END) as total_pnl,
                   ROUND((COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate
            FROM signals
            WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') {time_filter}
            GROUP BY pair
            ORDER BY count DESC
            """
            return self.db.execute_query(pair_stats_query)
        except Exception as e:
            self.logger.error(f"Error getting pair statistics: {e}")
            return []

    def _get_timeframe_statistics(self, time_filter: str = "") -> List[Dict[str, Any]]:
        """Pobierz statystyki per timeframe."""
        try:
            tf_stats_query = f"""
            SELECT timeframe_min,
                   COUNT(*) as total_signals,
                   COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
                   AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
                   ROUND((COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate
            FROM signals
            WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') {time_filter}
            GROUP BY timeframe_min
            ORDER BY timeframe_min
            """
            return self.db.execute_query(tf_stats_query)
        except Exception as e:
            self.logger.error(f"Error getting timeframe statistics: {e}")
            return []

    def get_pnl_over_time(self, days_filter: Optional[int] = None) -> List[Dict[str, Any]]:
        """Pobierz PnL w czasie dla wykresów."""
        try:
            time_filter = ""
            if days_filter:
                time_filter = f"AND close_timestamp >= datetime('now', '-{days_filter} days')"

            # POPRAWIONE: tylko zamknięte sygnały
            query = f"""
            SELECT DATE(close_timestamp) as date,
                   SUM(pnl) as daily_pnl,
                   COUNT(*) as signals_count
            FROM signals
            WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
              AND close_timestamp IS NOT NULL
              {time_filter}
            GROUP BY DATE(close_timestamp)
            ORDER BY date
            """

            results = self.db.execute_query(query)

            # Oblicz kumulatywny PnL
            cumulative_pnl = 0
            for result in results:
                cumulative_pnl += result['daily_pnl'] or 0
                result['cumulative_pnl'] = cumulative_pnl

            return results
        except Exception as e:
            self.logger.error(f"Error getting PnL over time: {e}")
            return []

    def get_pnl_distribution(self) -> List[Dict[str, Any]]:
        """Pobierz rozkład PnL dla histogramu."""
        try:
            # POPRAWIONE: tylko zamknięte sygnały
            query = """
            SELECT pnl
            FROM signals
            WHERE pnl IS NOT NULL AND status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
            ORDER BY pnl
            """

            results = self.db.execute_query(query)
            if not results:
                return []

            pnl_values = [r['pnl'] for r in results]

            # Utwórz histogram
            bins = 20
            hist, bin_edges = pd.cut(pnl_values, bins=bins, retbins=True)

            distribution = []
            for i in range(len(bin_edges) - 1):
                count = sum(1 for val in pnl_values if bin_edges[i] <= val < bin_edges[i + 1])
                if i == len(bin_edges) - 2:  # Ostatni przedział
                    count = sum(1 for val in pnl_values if bin_edges[i] <= val <= bin_edges[i + 1])

                percentage = (count / len(pnl_values)) * 100 if len(pnl_values) > 0 else 0.0

                distribution.append({
                    'range_start': float(bin_edges[i]),
                    'range_end': float(bin_edges[i + 1]),
                    'count': count,
                    'percentage': percentage
                })

            return distribution
        except Exception as e:
            self.logger.error(f"Error getting PnL distribution: {e}")
            return []

    def get_pairs_list(self) -> List[str]:
        """Pobierz listę wszystkich par."""
        try:
            query = "SELECT DISTINCT pair FROM signals ORDER BY pair"
            results = self.db.execute_query(query)
            return [r['pair'] for r in results]
        except Exception as e:
            self.logger.error(f"Error getting pairs list: {e}")
            return []

# === FLASK APPLICATION ===
class DashboardApp:
    """Główna klasa aplikacji Dashboard."""

    def __init__(self, config: Config):
        self.config = config
        self.logger = setup_logging()

        # Inicjalizacja komponentów
        self.db_manager = DatabaseManager(config.DB_PATH)
        self.signal_analyzer = SignalAnalyzer(self.db_manager)

        # Inicjalizacja Flask
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = config.SECRET_KEY
        self.app.config['JSON_SORT_KEYS'] = False

        # Inicjalizacja SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # Rejestracja routes
        self._register_routes()
        self._register_websocket_handlers()

        self.logger.info("Dashboard application initialized successfully")

    def _register_routes(self):
        """Rejestruje wszystkie routes aplikacji."""

        @self.app.route('/')
        def dashboard():
            """Główna strona dashboard - Production Ready UI."""
            self.logger.info("Production Dashboard page requested")
            return render_template('dashboard_production.html')

        @self.app.route('/old')
        def dashboard_old():
            """Stary dashboard dla porównania."""
            self.logger.info("Old Dashboard page requested")
            return render_template('dashboard.html')

        @self.app.route('/epic')
        def dashboard_epic():
            """Epic dashboard z animacjami."""
            self.logger.info("Epic Dashboard page requested")
            return render_template('dashboard_new.html')

        @self.app.route('/api/signals')
        def api_signals():
            """API endpoint dla sygnałów."""
            try:
                limit = request.args.get('limit', type=int)
                status = request.args.get('status')
                pair = request.args.get('pair')

                df = self.signal_analyzer.get_all_signals(limit)

                # Filtrowanie
                if status and status != 'all':
                    df = df[df['status'] == status]
                if pair and pair != 'all':
                    df = df[df['pair'] == pair]

                # Konwersja do JSON-friendly format
                signals = df.to_dict('records')
                for signal in signals:
                    # Formatowanie timestampów
                    if signal.get('timestamp'):
                        signal['timestamp'] = pd.to_datetime(signal['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                    if signal.get('close_timestamp'):
                        signal['close_timestamp'] = pd.to_datetime(signal['close_timestamp']).strftime('%Y-%m-%d %H:%M:%S')

                    # POPRAWKA PnL: Dodaj pnl_percent tylko jeśli PnL istnieje
                    pnl_value = signal.get('pnl')
                    if pnl_value is not None:
                        signal['pnl_percent'] = f"{pnl_value:.2%}"
                    else:
                        signal['pnl_percent'] = None

                return jsonify(JSONSanitizer.sanitize(signals))
            except Exception as e:
                self.logger.error(f"Error in api_signals: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/statistics')
        def api_statistics():
            """API endpoint dla statystyk."""
            try:
                self.logger.info("Statistics API requested")
                days_filter = request.args.get('days', type=int)
                stats = self.signal_analyzer.get_statistics(days_filter)
                return jsonify(JSONSanitizer.sanitize(stats))
            except Exception as e:
                self.logger.error(f"Error in api_statistics: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/performance-metrics')
        def api_performance_metrics():
            """API endpoint dla zaawansowanych metryk wydajności."""
            try:
                days_filter = request.args.get('days', type=int)
                stats = self.signal_analyzer.get_statistics(days_filter)

                # ROZSZERZONE METRYKI WYDAJNOŚCI
                performance_metrics = {
                    'sharpe_ratio': stats.get('sharpe_ratio', 0),
                    'sortino_ratio': stats.get('sortino_ratio', 0),  # NOWA METRYKA
                    'max_drawdown': stats.get('max_drawdown', 0),
                    'calmar_ratio': stats.get('calmar_ratio', 0),  # NOWA METRYKA
                    'max_consecutive_wins': stats.get('max_consecutive_wins', 0),
                    'max_consecutive_losses': stats.get('max_consecutive_losses', 0),
                    'profit_factor': stats.get('profit_factor', 0),
                    'avg_win': stats.get('avg_win', 0),
                    'avg_loss': stats.get('avg_loss', 0),
                    'total_pnl': stats.get('total_pnl', 0),
                    'win_rate': stats.get('win_rate', 0),  # DODANE
                    'total_signals': stats.get('total_signals', 0),  # DODANE
                    'closed_signals': stats.get('closed_signals', 0)  # DODANE
                }

                return jsonify(JSONSanitizer.sanitize(performance_metrics))
            except Exception as e:
                self.logger.error(f"Error in api_performance_metrics: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/pnl-chart')
        def api_pnl_chart():
            """API endpoint dla danych wykresu PnL."""
            try:
                days_filter = request.args.get('days', type=int)
                data = self.signal_analyzer.get_pnl_over_time(days_filter)
                return jsonify(JSONSanitizer.sanitize(data))
            except Exception as e:
                self.logger.error(f"Error in api_pnl_chart: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/pnl-distribution')
        def api_pnl_distribution():
            """API endpoint dla rozkładu PnL."""
            try:
                data = self.signal_analyzer.get_pnl_distribution()
                return jsonify(JSONSanitizer.sanitize(data))
            except Exception as e:
                self.logger.error(f"Error in api_pnl_distribution: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/pairs')
        def api_pairs():
            """API endpoint dla listy par."""
            try:
                pairs = self.signal_analyzer.get_pairs_list()
                return jsonify(pairs)
            except Exception as e:
                self.logger.error(f"Error in api_pairs: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/advanced-metrics')
        def api_advanced_metrics():
            """API endpoint dla zaawansowanych metryk - NOWY."""
            try:
                days_filter = request.args.get('days', type=int)
                stats = self.signal_analyzer.get_statistics(days_filter)

                # Dodatkowe obliczenia dla zaawansowanych metryk
                advanced_metrics = {
                    'risk_metrics': {
                        'sharpe_ratio': stats.get('sharpe_ratio', 0),
                        'sortino_ratio': stats.get('sortino_ratio', 0),
                        'calmar_ratio': stats.get('calmar_ratio', 0),
                        'max_drawdown': stats.get('max_drawdown', 0),
                        'max_drawdown_percent': stats.get('max_drawdown', 0) * 100
                    },
                    'performance_metrics': {
                        'total_pnl': stats.get('total_pnl', 0),
                        'total_pnl_percent': stats.get('total_pnl', 0) * 100,
                        'avg_pnl': stats.get('avg_pnl', 0),
                        'avg_pnl_percent': stats.get('avg_pnl', 0) * 100,
                        'profit_factor': stats.get('profit_factor', 0),
                        'win_rate': stats.get('win_rate', 0)
                    },
                    'trade_metrics': {
                        'total_signals': stats.get('total_signals', 0),
                        'closed_signals': stats.get('closed_signals', 0),
                        'winning_signals': stats.get('winning_signals', 0),
                        'avg_win': stats.get('avg_win', 0),
                        'avg_win_percent': stats.get('avg_win', 0) * 100,
                        'avg_loss': stats.get('avg_loss', 0),
                        'avg_loss_percent': stats.get('avg_loss', 0) * 100,
                        'max_consecutive_wins': stats.get('max_consecutive_wins', 0),
                        'max_consecutive_losses': stats.get('max_consecutive_losses', 0)
                    }
                }

                return jsonify(JSONSanitizer.sanitize(advanced_metrics))
            except Exception as e:
                self.logger.error(f"Error in api_advanced_metrics: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/webhook', methods=['POST'])
        def webhook():
            """Webhook do otrzymywania powiadomień od bota."""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No JSON data provided'}), 400

                event_type = data.get('type')
                event_data = data.get('data')

                if event_type == 'new_signal':
                    self._broadcast_new_signal(event_data)
                elif event_type == 'signal_update':
                    self._broadcast_signal_update(event_data)

                return jsonify({"status": "success"})
            except Exception as e:
                self.logger.error(f"Error in webhook: {e}")
                return jsonify({"status": "error", "message": str(e)}), 400

        @self.app.route('/health')
        def health_check():
            """Health check endpoint."""
            try:
                # Sprawdź połączenie z bazą danych
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

                return jsonify({
                    'status': 'healthy',
                    'timestamp': datetime.now().isoformat(),
                    'database': 'connected'
                })
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                return jsonify({
                    'status': 'unhealthy',
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e)
                }), 500

        @self.app.errorhandler(404)
        def not_found(error):
            """Handler dla błędu 404."""
            return jsonify({'error': 'Not found'}), 404

        @self.app.errorhandler(500)
        def internal_error(error):
            """Handler dla błędu 500."""
            self.logger.error(f"Internal server error: {error}")
            return jsonify({'error': 'Internal server error'}), 500

    def _register_websocket_handlers(self):
        """Rejestruje handlery WebSocket."""

        @self.socketio.on('connect')
        def handle_connect():
            """Obsługa połączenia WebSocket."""
            self.logger.info('Client connected')
            emit('status', {'msg': 'Connected to dashboard'})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Obsługa rozłączenia WebSocket."""
            self.logger.info('Client disconnected')

    def _broadcast_new_signal(self, signal_data: Dict[str, Any]):
        """Wyślij nowy sygnał do wszystkich połączonych klientów."""
        try:
            sanitized_data = JSONSanitizer.sanitize(signal_data)
            self.socketio.emit('new_signal', sanitized_data)
            self.logger.info(f"Broadcasted new signal: {signal_data.get('pair', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Error broadcasting new signal: {e}")

    def _broadcast_signal_update(self, signal_data: Dict[str, Any]):
        """Wyślij aktualizację sygnału do wszystkich połączonych klientów."""
        try:
            sanitized_data = JSONSanitizer.sanitize(signal_data)
            self.socketio.emit('signal_update', sanitized_data)
            self.logger.info(f"Broadcasted signal update: {signal_data.get('pair', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Error broadcasting signal update: {e}")

    def run(self):
        """Uruchom aplikację."""
        self.logger.info(f"🚀 Starting Production Dashboard on {self.config.HOST}:{self.config.PORT}")
        self.logger.info(f"📊 Dashboard available at: http://{self.config.HOST}:{self.config.PORT}")

        self.socketio.run(
            self.app,
            debug=self.config.DEBUG,
            host=self.config.HOST,
            port=self.config.PORT
        )

# === MAIN ENTRY POINT ===
def main():
    """Główna funkcja uruchamiająca aplikację."""
    try:
        config = Config.from_env()
        app = DashboardApp(config)
        app.run()
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to start application: {e}")
        raise

if __name__ == '__main__':
    main()
