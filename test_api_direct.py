#!/usr/bin/env python3
"""
Test bezpośrednio API dashboard.
"""

import requests
import json

def test_api_direct():
    """Test bezpośrednio API dashboard."""
    
    print("🔍 TEST BEZPOŚREDNIO API DASHBOARD")
    print("=" * 40)
    
    try:
        # Test API signals
        response = requests.get('http://localhost:5000/api/signals?limit=3')
        if response.status_code == 200:
            signals = response.json()
            
            print("Odpowiedź API:")
            for signal in signals:
                print(f"ID {signal['id']}: {signal['pair']} - Status: {signal['status']}")
                print(f"  PnL (raw): {repr(signal.get('pnl'))}")
                print(f"  PnL type: {type(signal.get('pnl'))}")
                if 'pnl_percent' in signal:
                    print(f"  PnL percent: {signal.get('pnl_percent')}")
                print()
        else:
            print(f"Błąd API: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Błąd: {e}")

if __name__ == "__main__":
    test_api_direct()
