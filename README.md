# Discord Bybit Signal Monitor

Bot Discord do monitorowania sygnałów tradingowych i automatycznego śledzenia ich wyników na giełdzie Bybit.

## 🚀 NOWA WERSJA PRODUKCYJNA 2.0

**Dostępne są teraz 3 wersje dashboard:**

- **`/` (Production)** - Nowy, zoptymalizowany dashboard bez animacji, gotowy do produkcji
- **`/epic` (Epic)** - Dashboard z pełnymi efektami wizualnymi i animacjami
- **`/old` (Legacy)** - Oryginalny dashboard dla porównania

### Uruchamianie wersji produkcyjnej:

```bash
# Zainstaluj zależności produkcyjne
pip install -r requirements_production.txt

# Skopiuj i skonfiguruj plik środowiskowy
cp .env.production .env
# Edytuj .env z własnymi ustawieniami

# Uruchom wersję produkcyjną
python run_production.py
```

## Funkcjonalności

### 🔍 Zaawansowane wykrywanie sygnałów

- **Wielokrotne formaty sygnałów** - obsługa różnych stylów pisania (BUY/SELL, LONG/SHORT)
- **Obsługa emoji i formatowania** - rozpoznawanie sygnałów z emoji (🔥💎📈📉⚡🎯)
- **Wielokrotne TP/SL** - obsługa sygnałów z TP1, TP2, TP3
- **Inteligentna walidacja** - sprawdzanie logiki sygnałów (TP/SL względem Entry)
- **Różne separatory** - obsługa przecinków i kropek jako separatory dziesiętne
- **Elastyczne pary** - automatyczna normalizacja par (ETH.P → ETHUSDT)

### 📊 Monitoring i analiza

- **Monitoring cen w czasie rzeczywistym** przez API Bybit z retry mechanizmem
- **Automatyczne zamykanie pozycji** przy osiągnięciu TP/SL lub timeout (domyślnie 48h)
- **Obsługa sygnałów od botów** - konfigurowalny whitelist dozwolonych botów
- **Zaawansowane statystyki wydajności** z profesjonalnymi metrykami
- **Logowanie do pliku** - szczegółowe logi w signal_monitor.log

### 💾 Zarządzanie danymi

- **Baza danych SQLite** do przechowywania historii
- **Dashboard Web** z wykresami i analizą w czasie rzeczywistym
- **Import historycznych sygnałów** z Discord
- **Eksport danych** do CSV

## Wymagania

- Python 3.8+
- Konto Discord Developer (dla bota)
- Konto Bybit z API key (opcjonalne dla testów)

## Instalacja

1. **Sklonuj/pobierz projekt**

```bash
git clone <repository_url>
cd discord-bybit-signal-monitor
```

2. **Zainstaluj zależności**

```bash
pip install -r requirements.txt
```

3. **Skonfiguruj zmienne środowiskowe**

```bash
cp .env.example .env
```

Edytuj plik `.env` i uzupełnij:

- `DISCORD_TOKEN` - token bota Discord
- `DISCORD_CHANNEL_ID` - ID kanału do monitorowania
- `BYBIT_API_KEY` i `BYBIT_API_SECRET` - klucze API Bybit
- `ALLOW_BOT_MESSAGES` - czy zezwalać na sygnały od botów (true/false)
- `BOT_WHITELIST` - lista ID botów dozwolonych do wysyłania sygnałów (oddzielone przecinkami)
- `SIGNAL_VALIDITY_HOURS` - czas ważności sygnału w godzinach (domyślnie 48)

## Konfiguracja Discord Bot

1. Idź na https://discord.com/developers/applications
2. Utwórz nową aplikację i bota
3. Skopiuj token bota do `.env`
4. Włącz "Message Content Intent" w ustawieniach bota
5. Dodaj bota do serwera z uprawnieniami:
   - Read Messages
   - Send Messages
   - Read Message History

## Konfiguracja Bybit API

1. Zaloguj się na Bybit
2. Idź do API Management
3. Utwórz nowy API key z uprawnieniami do odczytu (Read)
4. Skopiuj klucze do `.env`

## Konfiguracja obsługi botów

System może odbierać sygnały zarówno od użytkowników jak i od botów/aplikacji. Konfiguracja w pliku `.env`:

### Wyłączenie obsługi botów (domyślne):

```
ALLOW_BOT_MESSAGES=false
```

### Włączenie obsługi wszystkich botów:

```
ALLOW_BOT_MESSAGES=true
BOT_WHITELIST=
```

### Włączenie tylko wybranych botów (zalecane):

```
ALLOW_BOT_MESSAGES=true
BOT_WHITELIST=1234567890123456789,9876543210987654321
```

### Konfiguracja czasu ważności sygnałów:

```
SIGNAL_VALIDITY_HOURS=48
```

**WAŻNE**: Bot rozróżnia między timeframe wykresu a ważnością sygnału:

- **Timeframe wykresu** (TF 1, TF 5, etc.) - informacja z sygnału o tym, na jakim wykresie czasowym został wykryty (tylko informacyjna)
- **Ważność sygnału** - zawsze 48 godzin niezależnie od timeframe wykresu

Wszystkie sygnały są automatycznie zamykane jako "timeout" po 48 godzinach, niezależnie od tego czy zawierają TF 1min, 5min czy inne.

**Jak znaleźć ID bota:**

1. Włącz tryb deweloperski w Discord (Ustawienia → Zaawansowane → Tryb deweloperski)
2. Kliknij prawym przyciskiem na bota i wybierz "Kopiuj ID"

## Uruchomienie

### Bot Discord

```bash
python discord_bybit_signal_monitor.py
```

### Dashboard Web

```bash
python dashboard.py
```

Dashboard będzie dostępny pod adresem: http://localhost:5000

### Import historycznych sygnałów

```bash
python import_discord_history.py
```

### Dodanie przykładowych danych (do testów)

```bash
python add_sample_signals.py
```

## Format sygnałów

Bot rozpoznaje sygnały w wielu formatach:

### Podstawowy format:

```
BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60
```

### Format z LONG/SHORT:

```
LONG SOLUSDT
Entry: 100.25
Take Profit: 105.00
Stop Loss: 95.50
Duration: 240
```

### Format z wieloma TP:

```
SHORT BNBUSDT
Entry = 300.00
TP1 = 290.00
TP2 = 285.00
TP3 = 280.00
SL = 310.00
TF: 180
```

### Format z emoji:

```
💎 SELL XRPUSDT 💎
📈 Entry @ 0.6500
🎯 Target: 0.6200
⚡ Stop: 0.6800
⏰ Time: 45
```

### Format z przecinkami:

```
BUY DOGEUSDT
Entry: 0,08500
TP: 0,09000
SL: 0,08000
Timeframe: 90
```

### Obsługiwane warianty:

- **Kierunki**: BUY, SELL, LONG, SHORT
- **Pary**: BTCUSDT, ETH.P, ADAUSDT, SOL (automatycznie dodaje USDT)
- **Entry**: Entry, Price, @
- **TP**: TP, Take Profit, Target (z obsługą TP1, TP2, TP3)
- **SL**: SL, Stop Loss, Stop
- **Timeframe**: Timeframe, Time, TF, Duration (opcjonalne, domyślnie 60 min)
- **Separatory**: -, :, =, spacje
- **Liczby**: obsługa przecinków i kropek jako separatory dziesiętne

## Komendy bota

- `!stats` - wyświetla statystyki sygnałów

## Dashboard Web

Dashboard oferuje zaawansowaną analizę sygnałów z profesjonalnymi metrykami wydajności:

### Podstawowe funkcjonalności:

- 📊 **Statystyki w czasie rzeczywistym** - win rate, średni PnL, całkowity PnL, liczba sygnałów
- ⏰ **Filtry czasowe** - analiza dla ostatnich 24h, 7 dni, 30 dni lub wszystkich danych
- 🔍 **Filtrowanie sygnałów** - po parze, statusie, limicie wyników
- 📋 **Tabela sygnałów** - szczegółowy widok wszystkich sygnałów z kolorowym kodowaniem
- 📥 **Eksport do CSV** - pobieranie danych do analizy
- 🔴 **Live updates** - automatyczne odświeżanie przy nowych sygnałach przez WebSocket

### Zaawansowane metryki wydajności:

- 📈 **Sharpe Ratio** - miara efektywności inwestycji skorygowana o ryzyko
- 📉 **Max Drawdown** - maksymalna strata od szczytu do dołka
- 🔥 **Consecutive Wins/Losses** - najdłuższa seria wygranych/przegranych
- ⚖️ **Profit Factor** - stosunek całkowitych zysków do strat
- 💰 **Średnie zyski/straty** - analiza rozkładu wyników

### Wykresy i wizualizacje:

- 📈 **Equity Curve** - krzywa kapitału pokazująca kumulatywny PnL w czasie
- 🥧 **Rozkład statusów** - wykres kołowy z podziałem na TP/SL/Timeout/Otwarte
- 📊 **Rozkład PnL** - histogram pokazujący rozkład wyników
- 🪙 **Wydajność per para** - porównanie wyników dla różnych par walutowych
- ⏱️ **Analiza per timeframe** - statystyki dla różnych ram czasowych (tabela)

### Dostęp do Dashboard:

1. Uruchom dashboard: `python dashboard.py`
2. Otwórz przeglądarkę: http://localhost:5000
3. Dashboard automatycznie połączy się z bazą danych

## Struktura bazy danych

Sygnały są przechowywane w tabeli `signals` z polami:

- `id` - unikalny identyfikator
- `message_id` - ID wiadomości Discord
- `pair` - para handlowa (np. BTCUSDT)
- `side` - kierunek (BUY/SELL)
- `entry` - cena wejścia
- `tp` - take profit
- `sl` - stop loss
- `timestamp` - czas utworzenia
- `timeframe_min` - czas życia sygnału w minutach (48h = 2880 minut)
- `status` - status sygnału:
  - `NEW` - nowy sygnał, nie osiągnął poziomu entry
  - `ENTRY_HIT` - sygnał osiągnął poziom entry **PO** otrzymaniu sygnału
  - `TP_HIT` - sygnał osiągnął take profit **PO** osiągnięciu entry
  - `SL_HIT` - sygnał osiągnął stop loss **PO** osiągnięciu entry
  - `EXPIRED` - sygnał wygasł po 48 godzinach
  - `historical` - sygnał historyczny (import)
- `close_timestamp` - czas zamknięcia
- `exit_price` - cena wyjścia
- `pnl` - zysk/strata w %
- `entry_hit_timestamp` - kiedy poziom entry został osiągnięty
- `entry_hit_price` - po jakiej cenie poziom entry został osiągnięty

## ⏰ Chronologia sygnałów

System śledzi **chronologię** osiągania poziomów, aby zapewnić poprawność:

1. **Otrzymanie sygnału** (timestamp) → Status: `NEW`
2. **Cena osiąga entry PO otrzymaniu sygnału** → Status: `ENTRY_HIT`
   - Zapisywany `entry_hit_timestamp` i `entry_hit_price`
3. **Cena osiąga TP/SL PO osiągnięciu entry** → Status: `TP_HIT`/`SL_HIT`
4. **48h bez zamknięcia** → Status: `EXPIRED`

**Kluczowe zasady:**

- Entry musi być osiągnięte **PO** otrzymaniu sygnału
- TP/SL może być sprawdzane tylko **PO** osiągnięciu entry
- System sprawdza `now > entry_hit_timestamp` przed sprawdzaniem TP/SL

## Bezpieczeństwo

⚠️ **UWAGA**: Bot tylko monitoruje ceny i nie wykonuje rzeczywistych transakcji. Służy wyłącznie do analizy wydajności sygnałów.

## Rozwiązywanie problemów

### Błąd "Missing required environment variables"

- Sprawdź czy plik `.env` istnieje i zawiera wszystkie wymagane zmienne

### Błąd "401 Unauthorized"

- Sprawdź poprawność tokena Discord
- Upewnij się, że bot ma włączony "Message Content Intent"

### Błąd połączenia z Bybit

- Sprawdź poprawność kluczy API
- Upewnij się, że klucze mają uprawnienia do odczytu

### Błędy JSON w Dashboard (NAPRAWIONE)

**Problem:** Dashboard wyświetlał błędy typu:

```
SyntaxError: Unexpected token 'N', ..."avg_pnl": NaN,
SyntaxError: Unexpected token 'I', ..."_factor": Infinity,
```

**Rozwiązanie:** Zaimplementowano automatyczną sanityzację JSON która:

- Zastępuje `NaN` → `0.0`
- Zastępuje `Infinity` → `999999.0`
- Obsługuje wszystkie API endpoint'y i WebSocket komunikację

**Status:** ✅ Naprawione w wersji z 2025-06-14

## 🚀 PLAN ULEPSZENIA DASHBOARD 2025

### Zaplanowane ulepszenia:

#### 🎨 **Wizualne i UX** ✅ COMPLETED

- [x] Zaawansowane animacje i przejścia CSS ✅
- [x] Interaktywne elementy z hover effects ✅
- [x] Particle system w tle ✅
- [x] Gradient animations ✅
- [x] Smooth scrolling i parallax effects ✅
- [x] Loading skeletons ✅
- [x] Micro-interactions ✅

#### 📊 **Nowe funkcjonalności**

- [ ] Advanced trading metrics dashboard
- [ ] Real-time price charts integration
- [ ] Risk management calculator
- [ ] Portfolio performance tracker
- [ ] Alert system z konfigurowalnymi powiadomieniami
- [ ] Trading journal z notatkami
- [ ] Backtesting simulator

#### 🔧 **Techniczne ulepszenia** ✅ COMPLETED

- [x] Progressive Web App (PWA) ✅
- [x] Service Worker dla offline mode ✅
- [x] Advanced caching strategies ✅
- [x] Performance optimizations ✅
- [x] Mobile-first responsive design ✅
- [x] Touch gestures support ✅
- [x] Keyboard shortcuts ✅

#### 📱 **Mobile Experience**

- [ ] Native mobile app feel
- [ ] Swipe gestures
- [ ] Pull-to-refresh
- [ ] Bottom navigation
- [ ] Haptic feedback simulation

#### 🎯 **Personalizacja**

- [ ] Customizable dashboard layout
- [ ] Theme switcher (dark/light/custom)
- [ ] Widget configuration
- [ ] Personal trading preferences
- [ ] Custom alerts and notifications

## 🎉 IMPLEMENTACJA ZAKOŃCZONA!

### ✅ Co zostało zaimplementowane:

#### 🚀 **Ultimate Trading Dashboard**

Nowa, kompletnie przeprojektowana wersja dashboard z:

- **🎨 Zaawansowane efekty wizualne:**

  - Particle system w tle z interaktywnymi cząsteczkami
  - Gradient animations z płynnymi przejściami kolorów
  - Neon glow effects i glassmorphism
  - Smooth loading screen z animowanym progress bar
  - Hover effects z 3D transformacjami
  - Micro-interactions na wszystkich elementach

- **📱 Progressive Web App (PWA):**

  - Service Worker z offline mode
  - App Manifest dla instalacji jako aplikacja
  - Advanced caching strategies
  - Push notifications support
  - Background sync capabilities

- **⚡ Performance & UX:**

  - Mobile-first responsive design
  - Touch gestures support
  - Keyboard shortcuts (Ctrl+R, F11, Ctrl+T)
  - Lazy loading i code splitting ready
  - Optimized animations z CSS transforms

- **🎯 Nowe funkcjonalności:**
  - Real-time WebSocket connections
  - Advanced notification system
  - Theme switcher (dark/light)
  - Fullscreen mode toggle
  - Auto-refresh co 30 sekund
  - Animated counters i statistics

#### 📂 **Nowe pliki:**

- `templates/dashboard_ultimate.html` - Główny Ultimate Dashboard
- `static/manifest.json` - PWA Manifest
- `static/sw.js` - Service Worker
- `TODO.md` - Szczegółowy plan rozwoju

#### 🔗 **Dostępne wersje:**

- `/` - **Ultimate Dashboard** (nowa, główna wersja)
- `/production` - Production Dashboard (stabilna)
- `/epic` - Epic Dashboard (z animacjami)
- `/old` - Legacy Dashboard (dla porównania)

### 🚀 Jak uruchomić Ultimate Dashboard:

```bash
# Uruchom serwer
python dashboard.py

# Otwórz w przeglądarce
http://localhost:5000
```

### 🎯 Kluczowe funkcje Ultimate Dashboard:

1. **Particle Background** - Interaktywne cząsteczki reagujące na mysz
2. **Loading Screen** - Profesjonalny ekran ładowania z animacjami
3. **Hero Section** - Imponująca sekcja główna z gradient text
4. **Neon Cards** - Karty statystyk z glow effects
5. **Filter System** - Zaawansowane filtry czasowe
6. **PWA Ready** - Możliwość instalacji jako aplikacja
7. **Keyboard Shortcuts** - Skróty klawiszowe dla power users
8. **Real-time Updates** - Live aktualizacje przez WebSocket

## Licencja

MIT License
