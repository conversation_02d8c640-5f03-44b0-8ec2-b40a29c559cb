#!/usr/bin/env python3
"""
Test zamkniętych sygnałów z PnL.
"""

import requests

def test_closed_signals():
    """Test zamkniętych sygnałów z PnL."""
    
    print("🔍 TEST ZAMKNIĘTYCH SYGNAŁÓW Z PnL")
    print("=" * 40)
    
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=30')
        if response.status_code == 200:
            signals = response.json()
            
            # Znajdź zamknięte sygnały
            closed_signals = [s for s in signals if s['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']]
            print(f"Zamknięte sygnały: {len(closed_signals)}")
            print()
            
            if closed_signals:
                print("Przykłady zamkniętych sygnałów:")
                for signal in closed_signals[:5]:
                    pnl = signal.get('pnl')
                    pnl_percent = signal.get('pnl_percent')
                    print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                    print(f"  PnL: {pnl} ({type(pnl).__name__})")
                    print(f"  PnL %: {pnl_percent}")
                    print()
            else:
                print("Brak zamkniętych sygnałów w próbce")
                
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")

if __name__ == "__main__":
    test_closed_signals()
