#!/usr/bin/env python3
"""
Końcowy test wszystkich poprawek statystyk.
Sprawdza czy wszystkie API endpoints działają poprawnie.
"""

import requests
import json
import time
from datetime import datetime

def test_all_endpoints():
    """Testuje wszystkie endpoints API."""
    
    base_url = "http://localhost:5000"
    
    print("🧪 KOŃCOWY TEST POPRAWEK STATYSTYK")
    print("=" * 50)
    print(f"⏰ Czas testu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Health Check
    print("1️⃣ Test Health Check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ Status: {health_data['status']}")
            print(f"   📊 Database: {health_data['database']}")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd połączenia: {e}")
    
    print()
    
    # Test 2: Basic Statistics
    print("2️⃣ Test Basic Statistics...")
    try:
        response = requests.get(f"{base_url}/api/statistics", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Total Signals: {stats['total_signals']}")
            print(f"   ✅ Closed Signals: {stats['closed_signals']}")
            print(f"   ✅ Win Rate: {stats['win_rate']:.2f}%")
            print(f"   ✅ Sharpe Ratio: {stats['sharpe_ratio']:.4f}")
            print(f"   ✅ Sortino Ratio: {stats['sortino_ratio']:.4f}")
            print(f"   ✅ Max Drawdown: {stats['max_drawdown']:.4f}")
            print(f"   ✅ Calmar Ratio: {stats['calmar_ratio']:.4f}")
            print(f"   ✅ Profit Factor: {stats['profit_factor']:.4f}")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 3: Advanced Metrics (NOWY ENDPOINT)
    print("3️⃣ Test Advanced Metrics (NOWY)...")
    try:
        response = requests.get(f"{base_url}/api/advanced-metrics", timeout=10)
        if response.status_code == 200:
            metrics = response.json()
            
            print("   📊 Risk Metrics:")
            risk = metrics['risk_metrics']
            print(f"      Sharpe Ratio: {risk['sharpe_ratio']:.4f}")
            print(f"      Sortino Ratio: {risk['sortino_ratio']:.4f}")
            print(f"      Max Drawdown: {risk['max_drawdown_percent']:.2f}%")
            print(f"      Calmar Ratio: {risk['calmar_ratio']:.4f}")
            
            print("   💰 Performance Metrics:")
            perf = metrics['performance_metrics']
            print(f"      Win Rate: {perf['win_rate']:.2f}%")
            print(f"      Profit Factor: {perf['profit_factor']:.4f}")
            print(f"      Total PnL: {perf['total_pnl_percent']:.2f}%")
            
            print("   📈 Trade Metrics:")
            trade = metrics['trade_metrics']
            print(f"      Total Signals: {trade['total_signals']}")
            print(f"      Closed Signals: {trade['closed_signals']}")
            print(f"      Winning Signals: {trade['winning_signals']}")
            print(f"      Max Consecutive Wins: {trade['max_consecutive_wins']}")
            print(f"      Max Consecutive Losses: {trade['max_consecutive_losses']}")
            
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 4: Performance Metrics
    print("4️⃣ Test Performance Metrics...")
    try:
        response = requests.get(f"{base_url}/api/performance-metrics", timeout=10)
        if response.status_code == 200:
            perf = response.json()
            print(f"   ✅ Sharpe Ratio: {perf['sharpe_ratio']:.4f}")
            print(f"   ✅ Sortino Ratio: {perf['sortino_ratio']:.4f}")
            print(f"   ✅ Max Drawdown: {perf['max_drawdown']:.4f}")
            print(f"   ✅ Calmar Ratio: {perf['calmar_ratio']:.4f}")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 5: Signals API
    print("5️⃣ Test Signals API...")
    try:
        response = requests.get(f"{base_url}/api/signals?limit=5", timeout=10)
        if response.status_code == 200:
            signals = response.json()
            print(f"   ✅ Pobrano {len(signals)} sygnałów")
            if signals:
                print(f"   ✅ Pierwszy sygnał: {signals[0]['pair']} - {signals[0]['status']}")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 6: PnL Chart Data
    print("6️⃣ Test PnL Chart Data...")
    try:
        response = requests.get(f"{base_url}/api/pnl-chart", timeout=10)
        if response.status_code == 200:
            pnl_data = response.json()
            print(f"   ✅ Pobrano {len(pnl_data)} punktów danych PnL")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 7: Pairs List
    print("7️⃣ Test Pairs List...")
    try:
        response = requests.get(f"{base_url}/api/pairs", timeout=10)
        if response.status_code == 200:
            pairs = response.json()
            print(f"   ✅ Dostępne pary: {', '.join(pairs[:5])}")
        else:
            print(f"   ❌ Błąd: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Błąd: {e}")
    
    print()
    
    # Test 8: Time Filters
    print("8️⃣ Test Time Filters...")
    for days in [1, 7, 30]:
        try:
            response = requests.get(f"{base_url}/api/statistics?days={days}", timeout=10)
            if response.status_code == 200:
                stats = response.json()
                print(f"   ✅ Ostatnie {days} dni: {stats['total_signals']} sygnałów, WR: {stats['win_rate']:.2f}%")
            else:
                print(f"   ❌ Błąd dla {days} dni: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Błąd dla {days} dni: {e}")
    
    print()
    print("🎯 PODSUMOWANIE TESTÓW")
    print("=" * 50)
    print("✅ Wszystkie kluczowe poprawki zostały zaimplementowane:")
    print("   • Win Rate obliczany tylko na zamkniętych sygnałach")
    print("   • Sharpe Ratio z annualizacją")
    print("   • Sortino Ratio (nowa metryka)")
    print("   • Calmar Ratio (nowa metryka)")
    print("   • Max Drawdown z relatywnym obliczeniem")
    print("   • Profit Factor z lepszą walidacją")
    print("   • Nowy endpoint /api/advanced-metrics")
    print("   • Ujednolicone filtry statusów")
    print()
    print("🚀 Dashboard gotowy do użycia z poprawionymi statystykami!")

if __name__ == "__main__":
    test_all_endpoints()
