#!/usr/bin/env python3
"""
Test problemu z to_dict() i None wartościami.
"""

import sqlite3
import pandas as pd
import numpy as np

def test_to_dict_issue():
    """Test problemu z konwersją DataFrame do dict."""
    
    conn = sqlite3.connect('signals.db')
    
    print("🔍 TEST PROBLEMU Z to_dict()")
    print("=" * 40)
    
    # Test 1: Raw SQL
    cursor = conn.cursor()
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 3")
    columns = [description[0] for description in cursor.description]
    rows = cursor.fetchall()
    
    data = []
    for row in rows:
        row_dict = {}
        for i, col in enumerate(columns):
            row_dict[col] = row[i]
        data.append(row_dict)
    
    df = pd.DataFrame(data)
    
    print("DataFrame:")
    print(df[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df.dtypes}")
    print()
    
    # Test 2: to_dict('records')
    print("to_dict('records'):")
    signals = df.to_dict('records')
    for signal in signals:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    print()
    
    # Test 3: Sprawdź czy są NaN
    print("Sprawdzenie NaN:")
    for signal in signals:
        pnl = signal['pnl']
        if pnl is None:
            print(f"ID {signal['id']}: PnL is None")
        elif pd.isna(pnl):
            print(f"ID {signal['id']}: PnL is NaN")
        elif isinstance(pnl, float) and pnl == 0.0:
            print(f"ID {signal['id']}: PnL is 0.0")
        else:
            print(f"ID {signal['id']}: PnL = {pnl}")
    print()
    
    # Test 4: Sprawdź czy problem jest w fillna
    print("Test fillna:")
    df_filled = df.fillna(0.0)
    signals_filled = df_filled.to_dict('records')
    for signal in signals_filled[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    print()
    
    # Test 5: Sprawdź czy problem jest w pandas dtype
    print("Test z dtype=object:")
    df_obj = pd.DataFrame(data, dtype=object)
    signals_obj = df_obj.to_dict('records')
    for signal in signals_obj[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    
    conn.close()

if __name__ == "__main__":
    test_to_dict_issue()
