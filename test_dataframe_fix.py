#!/usr/bin/env python3
"""
Test naprawy DataFrame z NULL wartościami.
"""

import sqlite3
import pandas as pd

def test_dataframe_fix():
    """Test różnych sposobów obsługi NULL w DataFrame."""
    
    conn = sqlite3.connect('signals.db')
    
    print("🔍 TEST NAPRAWY DATAFRAME Z NULL")
    print("=" * 40)
    
    # Test 1: Standardowy pandas
    print("\n1️⃣ Standardowy pandas:")
    df1 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5", conn)
    print(df1[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df1.dtypes}")
    
    # Test 2: Raw SQL z manual conversion
    print("\n2️⃣ Raw SQL z manual conversion:")
    cursor = conn.cursor()
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5")
    columns = [description[0] for description in cursor.description]
    rows = cursor.fetchall()
    
    data = []
    for row in rows:
        row_dict = {}
        for i, col in enumerate(columns):
            row_dict[col] = row[i]
        data.append(row_dict)
    
    df2 = pd.DataFrame(data)
    print(df2[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df2.dtypes}")
    
    # Test 3: Pandas z dtype=object dla pnl
    print("\n3️⃣ Pandas z dtype=object:")
    df3 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5", conn, dtype={'pnl': 'object'})
    print(df3[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df3.dtypes}")
    
    # Test 4: Sprawdź rzeczywiste wartości
    print("\n4️⃣ Rzeczywiste wartości z bazy:")
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5")
    for row in cursor.fetchall():
        print(f"ID {row[0]}: {row[1]} - {row[2]} - PnL: {repr(row[3])}")
    
    # Test 5: Konwersja do JSON
    print("\n5️⃣ Test konwersji do JSON:")
    signals = df3.to_dict('records')
    for signal in signals[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    
    conn.close()

if __name__ == "__main__":
    test_dataframe_fix()
