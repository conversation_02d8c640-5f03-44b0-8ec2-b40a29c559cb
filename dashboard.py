#!/usr/bin/env python3
"""
🚀 ULTIMATE TRADING DASHBOARD - Production Ready v3.0
Discord Bybit Signal Monitor - Professional Trading Analytics Platform

Zaawansowana aplikacja Flask z pełnym spektrum funkcjonalności:
- Real-time monitoring sygnałów tradingowych
- Profesjonalne metryki wydaj<PERSON>ci
- Zaawansowane wizualizacje i animacje
- Progressive Web App capabilities
- Production-ready performance optimizations

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-15
Wersja: 3.0 Ultimate Edition
"""

import os
import sqlite3
import json
import math
import time
import gzip
import hashlib
from datetime import datetime, timedelta, timezone
from functools import wraps, lru_cache
from flask import Flask, render_template, jsonify, request, Response, send_file
from flask_socketio import SocketIO, emit
from flask_compress import Compress
import pandas as pd
import numpy as np
from dotenv import load_dotenv
import logging
from typing import Dict, List, Optional, Any
import threading
from collections import defaultdict, deque

# ===== KONFIGURACJA I INICJALIZACJA =====
load_dotenv()

# Konfiguracja środowiska
DB_PATH = os.getenv('DB_PATH', 'signals.db')
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
CHANNEL_ID = os.getenv('DISCORD_CHANNEL_ID')
CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', '300'))  # 5 minut
MAX_REQUESTS_PER_MINUTE = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '100'))

# Inicjalizacja Flask z optymalizacjami
app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.getenv('SECRET_KEY', 'ultimate-trading-dashboard-2025'),
    'JSON_SORT_KEYS': False,
    'COMPRESS_MIMETYPES': [
        'text/html', 'text/css', 'text/xml', 'application/json',
        'application/javascript', 'text/javascript'
    ],
    'COMPRESS_LEVEL': 6,
    'COMPRESS_MIN_SIZE': 500
})

# Inicjalizacja rozszerzeń
try:
    compress = Compress(app)
except:
    print("⚠️  Flask-Compress not available, continuing without compression")
    compress = None

socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# ===== CACHE I RATE LIMITING =====
cache = {}
cache_timestamps = {}
request_counts = defaultdict(lambda: deque())

# ===== LOGGING CONFIGURATION =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dashboard.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ===== UTILITY FUNCTIONS =====

def rate_limit(max_requests: int = MAX_REQUESTS_PER_MINUTE):
    """Decorator dla rate limiting API endpoints."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            now = time.time()

            # Czyść stare requesty (starsze niż 1 minuta)
            minute_ago = now - 60
            while request_counts[client_ip] and request_counts[client_ip][0] < minute_ago:
                request_counts[client_ip].popleft()

            # Sprawdź limit
            if len(request_counts[client_ip]) >= max_requests:
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Maximum {max_requests} requests per minute'
                }), 429

            # Dodaj aktualny request
            request_counts[client_ip].append(now)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def cache_response(timeout: int = CACHE_TIMEOUT):
    """Decorator dla cache'owania odpowiedzi API."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Utwórz klucz cache na podstawie nazwy funkcji i argumentów
            cache_key = f"{f.__name__}:{request.full_path}"
            now = time.time()

            # Sprawdź czy mamy świeże dane w cache
            if (cache_key in cache and
                cache_key in cache_timestamps and
                now - cache_timestamps[cache_key] < timeout):
                logger.debug(f"Cache hit for {cache_key}")
                return cache[cache_key]

            # Wykonaj funkcję i zapisz w cache
            result = f(*args, **kwargs)
            cache[cache_key] = result
            cache_timestamps[cache_key] = now
            logger.debug(f"Cache miss for {cache_key}, stored new result")
            return result
        return decorated_function
    return decorator

def compress_response(data: Any) -> Response:
    """Kompresuje odpowiedź JSON jeśli to możliwe."""
    json_str = json.dumps(data, ensure_ascii=False)

    # Sprawdź czy klient obsługuje gzip
    if 'gzip' in request.headers.get('Accept-Encoding', ''):
        try:
            compressed = gzip.compress(json_str.encode('utf-8'))
            response = Response(
                compressed,
                mimetype='application/json',
                headers={'Content-Encoding': 'gzip'}
            )
            return response
        except Exception as e:
            logger.warning(f"Compression failed: {e}")

    return jsonify(data)

def sanitize_for_json(obj):
    """
    Sanityzuje obiekt do bezpiecznego formatu JSON, zastępując NaN i Infinity.

    Args:
        obj: Obiekt do sanityzacji (może być dict, list, float, int, etc.)

    Returns:
        Obiekt z zastąpionymi problematycznymi wartościami
    """
    if isinstance(obj, dict):
        return {key: sanitize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, float):
        if math.isnan(obj):
            return 0.0  # Zastąp NaN zerem
        elif math.isinf(obj):
            if obj > 0:
                return 999999.0  # Zastąp +Infinity dużą liczbą
            else:
                return -999999.0  # Zastąp -Infinity dużą ujemną liczbą
        else:
            return obj
    elif isinstance(obj, np.floating):
        if np.isnan(obj):
            return 0.0
        elif np.isinf(obj):
            if obj > 0:
                return 999999.0
            else:
                return -999999.0
        else:
            return float(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    else:
        return obj

class SignalDatabase:
    """
    🚀 ULTIMATE Signal Database Manager

    Zaawansowana klasa do zarządzania bazą danych sygnałów z:
    - Connection pooling
    - Query optimization
    - Advanced analytics
    - Real-time monitoring
    - Performance metrics
    """

    def __init__(self, db_path=DB_PATH):
        self.db_path = db_path
        self._connection_pool = []
        self._pool_lock = threading.Lock()
        self._max_connections = 10
        logger.info(f"🗄️  Initialized SignalDatabase with path: {db_path}")

    def get_connection(self):
        """Uzyskaj połączenie z bazą danych z connection pooling."""
        with self._pool_lock:
            if self._connection_pool:
                conn = self._connection_pool.pop()
                # Sprawdź czy połączenie jest aktywne
                try:
                    conn.execute("SELECT 1")
                    return conn
                except sqlite3.Error:
                    pass

            # Utwórz nowe połączenie
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.execute("PRAGMA journal_mode=WAL")  # Optymalizacja wydajności
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            return conn

    def return_connection(self, conn):
        """Zwróć połączenie do puli."""
        with self._pool_lock:
            if len(self._connection_pool) < self._max_connections:
                self._connection_pool.append(conn)
            else:
                conn.close()

    @lru_cache(maxsize=128)
    def get_cached_query(self, query_hash: str, query: str, params: tuple = ()):
        """Cache dla często wykonywanych zapytań."""
        conn = self.get_connection()
        try:
            df = pd.read_sql_query(query, conn, params=params)
            return df
        finally:
            self.return_connection(conn)

    def get_all_signals(self, limit=None, filters=None):
        """
        🔍 Pobierz wszystkie sygnały z zaawansowanym filtrowaniem.

        Args:
            limit: Maksymalna liczba wyników
            filters: Dict z filtrami (pair, status, side, date_from, date_to)
        """
        conn = self.get_connection()
        try:
            query = """
            SELECT id, message_id, pair, side, entry, tp, sl,
                   timestamp, timeframe_min, status, close_timestamp,
                   exit_price, pnl, entry_hit_timestamp, entry_hit_price
            FROM signals
            WHERE 1=1
            """
            params = []

            # Dodaj filtry
            if filters:
                if filters.get('pair'):
                    query += " AND pair = ?"
                    params.append(filters['pair'])
                if filters.get('status'):
                    query += " AND status = ?"
                    params.append(filters['status'])
                if filters.get('side'):
                    query += " AND side = ?"
                    params.append(filters['side'])
                if filters.get('date_from'):
                    query += " AND timestamp >= ?"
                    params.append(filters['date_from'])
                if filters.get('date_to'):
                    query += " AND timestamp <= ?"
                    params.append(filters['date_to'])

            query += " ORDER BY timestamp DESC"

            if limit:
                query += f" LIMIT {limit}"

            df = pd.read_sql_query(query, conn, params=params)
            return df
        finally:
            self.return_connection(conn)

    def get_real_time_metrics(self):
        """📊 Pobierz metryki w czasie rzeczywistym."""
        conn = self.get_connection()
        try:
            # Ostatnie 24h
            query_24h = """
            SELECT
                COUNT(*) as signals_24h,
                COUNT(CASE WHEN status = 'TP_HIT' THEN 1 END) as tp_hits_24h,
                COUNT(CASE WHEN status = 'SL_HIT' THEN 1 END) as sl_hits_24h,
                AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl_24h
            FROM signals
            WHERE timestamp >= datetime('now', '-24 hours')
            """

            # Aktywne sygnały
            query_active = """
            SELECT COUNT(*) as active_signals
            FROM signals
            WHERE status IN ('NEW', 'ENTRY_HIT')
            """

            # Najlepsze pary ostatnie 7 dni
            query_top_pairs = """
            SELECT pair,
                   COUNT(*) as count,
                   AVG(pnl) as avg_pnl,
                   SUM(pnl) as total_pnl
            FROM signals
            WHERE timestamp >= datetime('now', '-7 days')
              AND pnl IS NOT NULL
            GROUP BY pair
            ORDER BY total_pnl DESC
            LIMIT 5
            """

            metrics_24h = pd.read_sql_query(query_24h, conn).iloc[0].to_dict()
            active = pd.read_sql_query(query_active, conn).iloc[0].to_dict()
            top_pairs = pd.read_sql_query(query_top_pairs, conn).to_dict('records')

            return {
                **metrics_24h,
                **active,
                'top_pairs_7d': top_pairs,
                'timestamp': datetime.now().isoformat()
            }
        finally:
            self.return_connection(conn)

    def get_statistics(self, days_filter=None):
        """Oblicz rozszerzone statystyki sygnałów."""
        conn = self.get_connection()

        # Filtr czasowy
        time_filter = ""
        if days_filter:
            time_filter = f"AND timestamp >= datetime('now', '-{days_filter} days')"

        # Podstawowe statystyki
        stats_query = f"""
        SELECT
            COUNT(*) as total_signals,
            COUNT(CASE WHEN status NOT IN ('NEW', 'ENTRY_HIT') THEN 1 END) as closed_signals,
            COUNT(CASE WHEN pnl > 0 THEN 1 END) as winning_signals,
            COUNT(CASE WHEN status = 'TP_HIT' THEN 1 END) as tp_hits,
            COUNT(CASE WHEN status = 'SL_HIT' THEN 1 END) as sl_hits,
            COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_signals,
            COUNT(CASE WHEN status = 'NEW' THEN 1 END) as new_signals,
            COUNT(CASE WHEN status = 'ENTRY_HIT' THEN 1 END) as entry_hit_signals,
            AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
            MAX(CASE WHEN pnl IS NOT NULL THEN pnl END) as max_pnl,
            MIN(CASE WHEN pnl IS NOT NULL THEN pnl END) as min_pnl,
            SUM(CASE WHEN pnl IS NOT NULL THEN pnl END) as total_pnl
        FROM signals
        WHERE 1=1 {time_filter}
        """

        cursor = conn.cursor()
        cursor.execute(stats_query)
        stats = cursor.fetchone()

        # Zaawansowane metryki
        advanced_stats = self._calculate_advanced_metrics(conn, time_filter)

        # Statystyki per para
        pair_stats_query = f"""
        SELECT pair,
               COUNT(*) as count,
               COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
               AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
               SUM(CASE WHEN pnl IS NOT NULL THEN pnl END) as total_pnl
        FROM signals
        WHERE status != 'open' {time_filter}
        GROUP BY pair
        ORDER BY count DESC
        """

        pair_stats = pd.read_sql_query(pair_stats_query, conn)

        # Statystyki per timeframe (tylko zamknięte sygnały)
        tf_stats_query = f"""
        SELECT timeframe_min,
               COUNT(*) as total_signals,
               COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
               AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
               ROUND((COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate
        FROM signals
        WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') {time_filter}
        GROUP BY timeframe_min
        ORDER BY timeframe_min
        """

        tf_stats = pd.read_sql_query(tf_stats_query, conn)
        conn.close()

        return {
            'total_signals': stats[0],
            'closed_signals': stats[1],
            'winning_signals': stats[2],
            'tp_hits': stats[3],
            'sl_hits': stats[4],
            'timeouts': stats[5],
            'avg_pnl': stats[6] or 0,
            'max_pnl': stats[7] or 0,
            'min_pnl': stats[8] or 0,
            'total_pnl': stats[9] or 0,
            'win_rate': (stats[2] / stats[1] * 100) if stats[1] > 0 else 0,
            'pair_stats': pair_stats.to_dict('records'),
            'timeframe_stats': tf_stats.to_dict('records'),
            **advanced_stats
        }

    def _calculate_advanced_metrics(self, conn, time_filter=""):
        """Oblicz zaawansowane metryki wydajności."""
        # Pobierz wszystkie zamknięte sygnały z PnL
        pnl_query = f"""
        SELECT pnl, close_timestamp
        FROM signals
        WHERE pnl IS NOT NULL AND status != 'open' {time_filter}
        ORDER BY close_timestamp
        """

        df = pd.read_sql_query(pnl_query, conn)

        if df.empty:
            return {
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'max_consecutive_wins': 0,
                'max_consecutive_losses': 0,
                'profit_factor': 0,
                'avg_win': 0,
                'avg_loss': 0
            }

        pnl_series = df['pnl']

        # Sharpe Ratio (uproszczony - bez risk-free rate)
        pnl_std = pnl_series.std()
        if pnl_std > 0 and not pd.isna(pnl_std):
            sharpe_ratio = pnl_series.mean() / pnl_std
            if pd.isna(sharpe_ratio):
                sharpe_ratio = 0.0
        else:
            sharpe_ratio = 0.0

        # Max Drawdown
        cumulative_pnl = pnl_series.cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = (cumulative_pnl - running_max)
        max_drawdown_val = drawdown.min()
        max_drawdown = abs(max_drawdown_val) if not drawdown.empty and not pd.isna(max_drawdown_val) else 0.0

        # Consecutive wins/losses
        wins_losses = (pnl_series > 0).astype(int)
        consecutive_wins = self._max_consecutive(wins_losses, 1)
        consecutive_losses = self._max_consecutive(wins_losses, 0)

        # Profit Factor - bezpieczne obliczenie
        total_wins = pnl_series[pnl_series > 0].sum()
        total_losses = abs(pnl_series[pnl_series < 0].sum())

        if pd.isna(total_wins):
            total_wins = 0.0
        if pd.isna(total_losses):
            total_losses = 0.0

        if total_losses > 0:
            profit_factor = total_wins / total_losses
            if pd.isna(profit_factor) or math.isinf(profit_factor):
                profit_factor = 999999.0  # Zastąp problematyczne wartości
        else:
            profit_factor = 999999.0 if total_wins > 0 else 0.0

        # Średnie zyski/straty - bezpieczne obliczenie
        if (pnl_series > 0).any():
            avg_win = pnl_series[pnl_series > 0].mean()
            if pd.isna(avg_win):
                avg_win = 0.0
        else:
            avg_win = 0.0

        if (pnl_series < 0).any():
            avg_loss = pnl_series[pnl_series < 0].mean()
            if pd.isna(avg_loss):
                avg_loss = 0.0
        else:
            avg_loss = 0.0

        return {
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }

    def _max_consecutive(self, series, value):
        """Oblicz maksymalną liczbę kolejnych wystąpień wartości."""
        if series.empty:
            return 0

        consecutive = 0
        max_consecutive = 0

        for val in series:
            if val == value:
                consecutive += 1
                max_consecutive = max(max_consecutive, consecutive)
            else:
                consecutive = 0

        return max_consecutive

    def get_pnl_over_time(self, days_filter=None):
        """Pobierz PnL w czasie dla wykresów."""
        conn = self.get_connection()

        time_filter = ""
        if days_filter:
            time_filter = f"AND close_timestamp >= datetime('now', '-{days_filter} days')"

        query = f"""
        SELECT DATE(close_timestamp) as date,
               SUM(pnl) as daily_pnl,
               COUNT(*) as signals_count
        FROM signals
        WHERE status != 'open' AND close_timestamp IS NOT NULL {time_filter}
        GROUP BY DATE(close_timestamp)
        ORDER BY date
        """

        df = pd.read_sql_query(query, conn)
        conn.close()

        if not df.empty:
            df['cumulative_pnl'] = df['daily_pnl'].cumsum()

        return df.to_dict('records')

    def get_pnl_distribution(self):
        """Pobierz rozkład PnL dla histogramu."""
        conn = self.get_connection()
        query = """
        SELECT pnl
        FROM signals
        WHERE pnl IS NOT NULL AND status != 'open'
        ORDER BY pnl
        """

        df = pd.read_sql_query(query, conn)
        conn.close()

        if df.empty:
            return []

        # Utwórz histogram
        pnl_values = df['pnl'].values
        bins = 20  # Liczba przedziałów
        hist, bin_edges = pd.cut(pnl_values, bins=bins, retbins=True)

        distribution = []
        for i in range(len(bin_edges) - 1):
            count = ((pnl_values >= bin_edges[i]) & (pnl_values < bin_edges[i + 1])).sum()
            if i == len(bin_edges) - 2:  # Ostatni przedział - włącz górną granicę
                count = ((pnl_values >= bin_edges[i]) & (pnl_values <= bin_edges[i + 1])).sum()

            # Bezpieczne obliczenie procentów
            percentage = (count / len(pnl_values)) * 100 if len(pnl_values) > 0 else 0.0
            if pd.isna(percentage):
                percentage = 0.0

            distribution.append({
                'range_start': float(bin_edges[i]) if not pd.isna(bin_edges[i]) else 0.0,
                'range_end': float(bin_edges[i + 1]) if not pd.isna(bin_edges[i + 1]) else 0.0,
                'count': int(count),
                'percentage': percentage
            })

        return distribution

db = SignalDatabase()

# ===== MAIN DASHBOARD ROUTES =====

@app.route('/')
def dashboard():
    """🚀 Główna strona dashboard - Ultimate Trading Dashboard."""
    logger.info("🚀 Ultimate Trading Dashboard requested")
    return render_template('dashboard_ultimate.html')

@app.route('/production')
def dashboard_production():
    """📊 Production dashboard."""
    logger.info("📊 Production Dashboard requested")
    return render_template('dashboard_production.html')

@app.route('/epic')
def dashboard_epic():
    """🎆 Epic dashboard z animacjami."""
    logger.info("🎆 Epic Dashboard requested")
    return render_template('dashboard_new.html')

@app.route('/old')
def dashboard_old():
    """📜 Legacy dashboard dla porównania."""
    logger.info("� Legacy Dashboard requested")
    return render_template('dashboard.html')

# PWA Routes
@app.route('/manifest.json')
def manifest():
    """📱 PWA Manifest file."""
    return app.send_static_file('manifest.json')

@app.route('/sw.js')
def service_worker():
    """⚙️ Service Worker file."""
    response = app.send_static_file('sw.js')
    response.headers['Content-Type'] = 'application/javascript'
    response.headers['Service-Worker-Allowed'] = '/'
    return response

# ===== HEALTH CHECK & MONITORING =====

@app.route('/health')
@rate_limit(max_requests=60)
def health_check():
    """🏥 Health check endpoint."""
    try:
        # Sprawdź połączenie z bazą danych
        conn = db.get_connection()
        conn.execute("SELECT 1")
        db.return_connection(conn)

        # Sprawdź podstawowe metryki
        stats = db.get_statistics()

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected',
            'total_signals': stats.get('total_signals', 0),
            'cache_size': len(cache),
            'version': '3.0-ultimate'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system-info')
@rate_limit(max_requests=30)
def api_system_info():
    """🔧 Informacje o systemie."""
    return jsonify({
        'version': '3.0-ultimate',
        'python_version': os.sys.version,
        'cache_entries': len(cache),
        'active_connections': len(db._connection_pool),
        'uptime': time.time() - app.start_time if hasattr(app, 'start_time') else 0,
        'features': [
            'real_time_monitoring',
            'advanced_analytics',
            'rate_limiting',
            'response_caching',
            'connection_pooling',
            'progressive_web_app'
        ]
    })

@app.route('/api/signals')
def api_signals():
    """API endpoint dla sygnałów."""
    limit = request.args.get('limit', type=int)
    status = request.args.get('status')
    pair = request.args.get('pair')

    df = db.get_all_signals(limit)

    # Filtrowanie
    if status and status != 'all':
        df = df[df['status'] == status]
    if pair and pair != 'all':
        df = df[df['pair'] == pair]

    # Konwersja do JSON-friendly format
    signals = df.to_dict('records')
    for signal in signals:
        if signal['timestamp']:
            signal['timestamp'] = pd.to_datetime(signal['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        if signal['close_timestamp']:
            signal['close_timestamp'] = pd.to_datetime(signal['close_timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        if signal['pnl']:
            signal['pnl_percent'] = f"{signal['pnl']:.2%}"

    # Sanityzacja danych przed wysłaniem
    sanitized_signals = sanitize_for_json(signals)
    return jsonify(sanitized_signals)

@app.route('/api/statistics')
def api_statistics():
    """API endpoint dla statystyk."""
    print("📊 Statistics API requested")
    days_filter = request.args.get('days', type=int)
    stats = db.get_statistics(days_filter)
    sanitized_stats = sanitize_for_json(stats)
    return jsonify(sanitized_stats)

@app.route('/api/performance-metrics')
def api_performance_metrics():
    """API endpoint dla zaawansowanych metryk wydajności."""
    days_filter = request.args.get('days', type=int)
    stats = db.get_statistics(days_filter)

    # Wyciągnij tylko metryki wydajności
    performance_metrics = {
        'sharpe_ratio': stats.get('sharpe_ratio', 0),
        'max_drawdown': stats.get('max_drawdown', 0),
        'max_consecutive_wins': stats.get('max_consecutive_wins', 0),
        'max_consecutive_losses': stats.get('max_consecutive_losses', 0),
        'profit_factor': stats.get('profit_factor', 0),
        'avg_win': stats.get('avg_win', 0),
        'avg_loss': stats.get('avg_loss', 0),
        'total_pnl': stats.get('total_pnl', 0)
    }

    # Sanityzacja metryk wydajności
    sanitized_metrics = sanitize_for_json(performance_metrics)
    return jsonify(sanitized_metrics)

@app.route('/api/heatmap-data')
def api_heatmap_data():
    """API endpoint dla danych heatmapy wydajności."""
    conn = db.get_connection()

    # Pobierz dane dla heatmapy (para vs dzień)
    heatmap_query = """
    SELECT
        pair,
        DATE(close_timestamp) as date,
        SUM(pnl) as daily_pnl,
        COUNT(*) as signals_count
    FROM signals
    WHERE status != 'open' AND close_timestamp IS NOT NULL
    GROUP BY pair, DATE(close_timestamp)
    ORDER BY date DESC, pair
    """

    df = pd.read_sql_query(heatmap_query, conn)
    conn.close()

    # Sanityzacja danych heatmapy
    heatmap_data = sanitize_for_json(df.to_dict('records'))
    return jsonify(heatmap_data)

@app.route('/api/pnl-chart')
def api_pnl_chart():
    """API endpoint dla danych wykresu PnL."""
    days_filter = request.args.get('days', type=int)
    data = db.get_pnl_over_time(days_filter)
    sanitized_data = sanitize_for_json(data)
    return jsonify(sanitized_data)

@app.route('/api/pnl-distribution')
def api_pnl_distribution():
    """API endpoint dla rozkładu PnL."""
    data = db.get_pnl_distribution()
    sanitized_data = sanitize_for_json(data)
    return jsonify(sanitized_data)

@app.route('/api/pairs')
def api_pairs():
    """API endpoint dla listy par."""
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT pair FROM signals ORDER BY pair")
    pairs = [row[0] for row in cursor.fetchall()]
    conn.close()
    return jsonify(pairs)

@app.route('/api/webhook', methods=['POST'])
def webhook():
    """Webhook do otrzymywania powiadomień od bota."""
    try:
        data = request.get_json()
        event_type = data.get('type')
        event_data = data.get('data')

        if event_type == 'new_signal':
            broadcast_new_signal(event_data)
        elif event_type == 'signal_update':
            broadcast_signal_update(event_data)

        return jsonify({"status": "success"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 400

@socketio.on('connect')
def handle_connect():
    """Obsługa połączenia WebSocket."""
    print('Client connected')
    emit('status', {'msg': 'Connected to dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    """Obsługa rozłączenia WebSocket."""
    print('Client disconnected')

def broadcast_new_signal(signal_data):
    """Wyślij nowy sygnał do wszystkich połączonych klientów."""
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('new_signal', sanitized_data)

def broadcast_signal_update(signal_data):
    """Wyślij aktualizację sygnału do wszystkich połączonych klientów."""
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('signal_update', sanitized_data)

if __name__ == '__main__':
    print("🚀 Uruchamianie Dashboard Web...")
    print("📊 Dashboard dostępny pod adresem: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
