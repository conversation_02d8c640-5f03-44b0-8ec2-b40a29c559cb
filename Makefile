# Makefile for Discord Bybit Signal Monitor Dashboard
# Production-ready commands for easy management

.PHONY: help install install-prod setup run run-prod run-epic run-old test test-verbose clean backup status logs

# Default target
help:
	@echo "🚀 Discord Bybit Signal Monitor Dashboard - Production v2.0"
	@echo ""
	@echo "Available commands:"
	@echo "  📦 install      - Install development dependencies"
	@echo "  🏭 install-prod - Install production dependencies"
	@echo "  🔧 setup        - Setup environment (copy .env.production to .env)"
	@echo "  🚀 run          - Run production dashboard"
	@echo "  🎆 run-epic     - Run epic dashboard (with animations)"
	@echo "  📊 run-old      - Run legacy dashboard"
	@echo "  🧪 test         - Run production tests"
	@echo "  🔍 test-verbose - Run tests with verbose output"
	@echo "  📋 status       - Check dashboard status"
	@echo "  📝 logs         - Show dashboard logs"
	@echo "  🧹 clean        - Clean temporary files"
	@echo "  💾 backup       - Backup database and config"
	@echo ""
	@echo "Examples:"
	@echo "  make install-prod && make setup && make run"
	@echo "  make test-verbose"
	@echo "  make backup"

# Installation targets
install:
	@echo "📦 Installing development dependencies..."
	pip install -r requirements.txt
	@echo "✅ Development dependencies installed"

install-prod:
	@echo "🏭 Installing production dependencies..."
	pip install -r requirements_production.txt
	@echo "✅ Production dependencies installed"

# Setup target
setup:
	@echo "🔧 Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.production .env; \
		echo "📋 Copied .env.production to .env"; \
		echo "⚠️  Please edit .env with your configuration"; \
	else \
		echo "📋 .env file already exists"; \
	fi
	@echo "✅ Environment setup complete"

# Run targets
run:
	@echo "🚀 Starting production dashboard..."
	python run_production.py

run-prod:
	@echo "🏭 Starting production dashboard..."
	python run_production.py

run-epic:
	@echo "🎆 Starting epic dashboard (original with animations)..."
	python dashboard.py

run-old:
	@echo "📊 Starting legacy dashboard..."
	python dashboard.py

# Test targets
test:
	@echo "🧪 Running production tests..."
	python test_production.py

test-verbose:
	@echo "🔍 Running production tests (verbose)..."
	python test_production.py --verbose

test-endpoint:
	@echo "🎯 Testing specific endpoint..."
	@read -p "Enter endpoint (e.g., /api/statistics): " endpoint; \
	python test_production.py --endpoint "$$endpoint" --verbose

# Status and monitoring
status:
	@echo "📋 Checking dashboard status..."
	@curl -s http://localhost:5000/health | python -m json.tool || echo "❌ Dashboard not responding"

logs:
	@echo "📝 Showing dashboard logs..."
	@if [ -f dashboard.log ]; then \
		tail -f dashboard.log; \
	else \
		echo "❌ Log file not found"; \
	fi

logs-tail:
	@echo "📝 Showing last 50 lines of logs..."
	@if [ -f dashboard.log ]; then \
		tail -n 50 dashboard.log; \
	else \
		echo "❌ Log file not found"; \
	fi

# Maintenance targets
clean:
	@echo "🧹 Cleaning temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.log.*" -delete
	@echo "✅ Cleanup complete"

backup:
	@echo "💾 Creating backup..."
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if [ -f signals.db ]; then \
		cp signals.db backups/signals_$$timestamp.db; \
		echo "📊 Database backed up to backups/signals_$$timestamp.db"; \
	fi; \
	if [ -f .env ]; then \
		cp .env backups/env_$$timestamp.backup; \
		echo "🔧 Config backed up to backups/env_$$timestamp.backup"; \
	fi
	@echo "✅ Backup complete"

restore:
	@echo "🔄 Restoring from backup..."
	@ls -la backups/
	@read -p "Enter backup timestamp (YYYYMMDD_HHMMSS): " timestamp; \
	if [ -f "backups/signals_$$timestamp.db" ]; then \
		cp "backups/signals_$$timestamp.db" signals.db; \
		echo "✅ Database restored from backups/signals_$$timestamp.db"; \
	else \
		echo "❌ Backup file not found"; \
	fi

# Development targets
dev-setup:
	@echo "🛠️ Setting up development environment..."
	make install
	make setup
	@echo "✅ Development environment ready"

prod-setup:
	@echo "🏭 Setting up production environment..."
	make install-prod
	make setup
	@echo "✅ Production environment ready"

# Docker targets
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t discord-bybit-dashboard .

docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 5000:5000 --env-file .env discord-bybit-dashboard

# Health checks
health:
	@echo "🏥 Running health checks..."
	@python -c "import requests; r=requests.get('http://localhost:5000/health'); print('✅ Dashboard healthy' if r.status_code==200 else '❌ Dashboard unhealthy')" 2>/dev/null || echo "❌ Dashboard not responding"

ping:
	@echo "🏓 Pinging dashboard..."
	@curl -s -o /dev/null -w "Status: %{http_code}, Time: %{time_total}s\n" http://localhost:5000/ || echo "❌ Dashboard not responding"

# Database management
db-status:
	@echo "🗄️ Database status..."
	@if [ -f signals.db ]; then \
		echo "✅ Database file exists"; \
		echo "📊 Size: $$(du -h signals.db | cut -f1)"; \
		echo "📅 Modified: $$(stat -c %y signals.db 2>/dev/null || stat -f %Sm signals.db)"; \
	else \
		echo "❌ Database file not found"; \
	fi

db-info:
	@echo "🗄️ Database information..."
	@python -c "import sqlite3; conn=sqlite3.connect('signals.db'); cursor=conn.cursor(); cursor.execute('SELECT COUNT(*) FROM signals'); print(f'Total signals: {cursor.fetchone()[0]}'); conn.close()" 2>/dev/null || echo "❌ Cannot read database"

# Quick commands
start: run
stop:
	@echo "🛑 Stopping dashboard..."
	@pkill -f "python.*dashboard" || echo "No dashboard process found"

restart: stop run

# All-in-one commands
quick-start:
	@echo "⚡ Quick start..."
	make install-prod
	make setup
	make run

full-setup:
	@echo "🎯 Full setup..."
	make install-prod
	make setup
	make backup
	make test
	@echo "🎉 Setup complete! Run 'make run' to start the dashboard."

# Help for specific topics
help-api:
	@echo "🔌 API Endpoints:"
	@echo "  GET  /                     - Production dashboard"
	@echo "  GET  /epic                 - Epic dashboard (animations)"
	@echo "  GET  /old                  - Legacy dashboard"
	@echo "  GET  /health               - Health check"
	@echo "  GET  /api/signals          - List signals"
	@echo "  GET  /api/statistics       - Basic statistics"
	@echo "  GET  /api/performance-metrics - Advanced metrics"
	@echo "  GET  /api/pnl-chart        - PnL chart data"
	@echo "  GET  /api/pairs            - Trading pairs list"
	@echo "  POST /api/webhook          - Webhook for notifications"

help-config:
	@echo "🔧 Configuration:"
	@echo "  1. Copy .env.production to .env"
	@echo "  2. Edit .env with your settings:"
	@echo "     - DISCORD_TOKEN"
	@echo "     - DISCORD_CHANNEL_ID"
	@echo "     - BYBIT_API_KEY"
	@echo "     - BYBIT_API_SECRET"
	@echo "     - SECRET_KEY (change default!)"
	@echo "  3. Run 'make run'"

help-troubleshooting:
	@echo "🔧 Troubleshooting:"
	@echo "  📋 Check status:     make status"
	@echo "  📝 View logs:        make logs"
	@echo "  🧪 Run tests:        make test-verbose"
	@echo "  🗄️ Check database:   make db-status"
	@echo "  🏥 Health check:     make health"
	@echo "  🏓 Ping dashboard:   make ping"
