#!/usr/bin/env python3
"""
Sprawdź rzeczywiste wartości PnL w bazie danych.
"""

import sqlite3

def check_db_pnl():
    """Sprawdź wartości PnL w bazie danych."""
    
    conn = sqlite3.connect('signals.db')
    cursor = conn.cursor()
    
    print("🔍 SPRAWDZENIE WARTOŚCI PnL W BAZIE DANYCH")
    print("=" * 50)
    
    # Sprawdź najnowsze sygnały
    cursor.execute("""
        SELECT id, pair, status, pnl, exit_price, entry
        FROM signals 
        WHERE id >= 20 
        ORDER BY id DESC
    """)
    
    signals = cursor.fetchall()
    print(f"{'ID':>3} | {'Pair':>8} | {'Status':>9} | {'PnL':>10} | {'Exit':>8} | {'Entry':>8}")
    print("-" * 60)
    
    for signal in signals:
        pnl_str = str(signal[3]) if signal[3] is not None else "NULL"
        exit_str = str(signal[4]) if signal[4] is not None else "NULL"
        entry_str = str(signal[5]) if signal[5] is not None else "NULL"
        
        print(f"{signal[0]:>3} | {signal[1]:>8} | {signal[2]:>9} | {pnl_str:>10} | {exit_str:>8} | {entry_str:>8}")
    
    # Sprawdź typy danych
    print(f"\n📊 ANALIZA TYPÓW DANYCH:")
    cursor.execute("PRAGMA table_info(signals)")
    columns = cursor.fetchall()
    
    for col in columns:
        if col[1] == 'pnl':
            print(f"Kolumna PnL: {col[1]} - typ: {col[2]} - nullable: {not col[3]}")
    
    # Sprawdź sygnały z rzeczywistym PnL
    cursor.execute("""
        SELECT COUNT(*) as count
        FROM signals 
        WHERE pnl IS NOT NULL AND pnl != 0
    """)
    
    real_pnl_count = cursor.fetchone()[0]
    print(f"\nSygnały z rzeczywistym PnL (nie NULL i nie 0): {real_pnl_count}")
    
    # Sprawdź sygnały z PnL = 0
    cursor.execute("""
        SELECT COUNT(*) as count
        FROM signals 
        WHERE pnl = 0
    """)
    
    zero_pnl_count = cursor.fetchone()[0]
    print(f"Sygnały z PnL = 0: {zero_pnl_count}")
    
    # Sprawdź sygnały z PnL = NULL
    cursor.execute("""
        SELECT COUNT(*) as count
        FROM signals 
        WHERE pnl IS NULL
    """)
    
    null_pnl_count = cursor.fetchone()[0]
    print(f"Sygnały z PnL = NULL: {null_pnl_count}")
    
    conn.close()

if __name__ == "__main__":
    check_db_pnl()
