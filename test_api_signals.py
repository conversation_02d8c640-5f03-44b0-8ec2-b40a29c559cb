#!/usr/bin/env python3
"""
Test API sygnałów - sprawdź jak zwracane są dane PnL.
"""

import requests
import json

def test_signals_api():
    """Testuje API sygnałów i sprawdza format danych PnL."""
    
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=10')
        if response.status_code == 200:
            signals = response.json()
            
            print("🔍 TEST API SYGNAŁÓW - FORMAT PnL")
            print("=" * 50)
            
            print(f"Pobrano {len(signals)} sygnałów")
            print()
            
            for i, signal in enumerate(signals[:5]):
                print(f"Sygnał {i+1}:")
                print(f"  ID: {signal.get('id')}")
                print(f"  Pair: {signal.get('pair')}")
                print(f"  Status: {signal.get('status')}")
                print(f"  PnL (raw): {repr(signal.get('pnl'))}")
                print(f"  PnL type: {type(signal.get('pnl'))}")
                
                # Sprawdź czy ma pnl_percent
                if 'pnl_percent' in signal:
                    print(f"  PnL percent: {signal.get('pnl_percent')}")
                
                print()
            
            # Sprawdź sygnały z PnL
            signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
            print(f"Sygnały z PnL: {len(signals_with_pnl)}")
            
            if signals_with_pnl:
                print("Przykłady sygnałów z PnL:")
                for signal in signals_with_pnl[:3]:
                    pnl = signal.get('pnl')
                    print(f"  {signal['pair']}: PnL = {pnl} ({type(pnl).__name__})")
        
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")

if __name__ == "__main__":
    test_signals_api()
