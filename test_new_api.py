#!/usr/bin/env python3
"""
Test nowego API z poprawkami.
"""

import requests
import json

def test_new_api():
    """Test nowego API z poprawkami."""
    
    print("🔍 TEST NOWEGO API Z POPRAWKAMI")
    print("=" * 40)
    
    try:
        # Test API signals z małą liczbą
        response = requests.get('http://localhost:5000/api/signals?limit=5')
        if response.status_code == 200:
            signals = response.json()
            
            print(f"Pobrano {len(signals)} sygnałów")
            print()
            
            for i, signal in enumerate(signals):
                print(f"Sygnał {i+1}:")
                print(f"  ID: {signal['id']}")
                print(f"  Pair: {signal['pair']}")
                print(f"  Status: {signal['status']}")
                print(f"  PnL: {repr(signal.get('pnl'))} ({type(signal.get('pnl')).__name__})")
                print(f"  PnL percent: {signal.get('pnl_percent')}")
                
                # Sprawdź czy to sygnał z rzeczywistym PnL
                if signal['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']:
                    print(f"  ⚠️  To powinien być sygnał z PnL!")
                else:
                    print(f"  ✅ To sygnał bez PnL (status: {signal['status']})")
                print()
                
            # Sprawdź sygnały z rzeczywistym PnL
            real_pnl_signals = [s for s in signals if s.get('pnl') is not None and s['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']]
            print(f"Sygnały z rzeczywistym PnL: {len(real_pnl_signals)}")
            
            # Sprawdź sygnały bez PnL
            no_pnl_signals = [s for s in signals if s.get('pnl') is None and s['status'] in ['NEW', 'ENTRY_HIT']]
            print(f"Sygnały bez PnL (poprawnie): {len(no_pnl_signals)}")
            
        else:
            print(f"Błąd API: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Błąd: {e}")

if __name__ == "__main__":
    test_new_api()
